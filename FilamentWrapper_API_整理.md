# FilamentWrapper.mm API 功能分类整理

## 📋 API 分类概览

FilamentWrapper.mm 是 iOS 3D 渲染应用的核心 C++ 包装器，提供了完整的 Filament 渲染引擎功能接口。已按功能模块重新组织，共分为以下9个主要类别：

---

## 🏗️ 1. 初始化和生命周期管理

### 核心初始化方法
```objc
- (instancetype)init                    // 主初始化方法
- (BOOL)initEngine                      // Filament引擎初始化
- (void)dealloc                         // 析构方法
- (void)cleanup                         // 资源清理
```

### 子系统初始化方法
```objc
- (void)_initView                       // 视图和场景初始化
- (void)_initRender                     // 渲染器初始化
- (void)_initTexture                    // 纹理提供器初始化
- (void)_initMaterial                   // 材质系统初始化
```

**功能说明：** 负责整个Filament引擎的启动、各子系统的初始化以及程序结束时的资源清理。包括 Metal 后端创建、场景管理器、材质提供器、资源加载器等核心组件的初始化。

---

## 🌅 2. 场景和光照管理

### 场景配置方法
```objc
- (void)setupLighting                   // 设置场景光照系统
- (void)setupCamera                     // 配置相机参数
- (void)setCameraDistance:(float)distance // 动态调整相机距离
```

**功能说明：** 
- **光照系统**：创建点光源（SUN类型）、设置环境光照、配置天空盒和间接光照
- **相机管理**：设置相机变换矩阵、投影参数、视野角度和近远平面
- **场景优化**：针对球体模型优化光照位置和强度，确保最佳视觉效果

---

## 🖼️ 3. 表面和视图管理

### 渲染表面方法
```objc
- (BOOL)setupWithMetalLayer:(CAMetalLayer *)metalLayer  // 设置Metal渲染表面
- (void)resizeWithWidth:(NSUInteger)width height:(NSUInteger)height // 调整视窗大小
```

**功能说明：** 
- **Metal集成**：创建和管理 SwapChain，与 iOS Metal 渲染层深度集成
- **动态调整**：支持屏幕旋转和窗口大小变化，自动调整视口和相机纵横比
- **性能优化**：确保渲染表面与设备屏幕参数匹配

---

## 🎨 4. 渲染系统

### 核心渲染方法
```objc
- (void)render                          // 执行每帧渲染
```

**功能说明：** 
- **渲染循环**：实现标准的 Filament 渲染流程 `beginFrame` → `render` → `endFrame`
- **性能监控**：每100帧输出一次调试信息，监控场景实体数量
- **错误处理**：检查渲染器、交换链和视图的有效性

---

## 📦 5. 模型加载和管理

### 模型加载核心方法
```objc
- (BOOL)loadModelFromData:(NSData *)data           // 从数据加载3D模型
- (BOOL)validateModelData:(NSData *)data           // 验证模型数据格式
- (void)diagnoseModelLoadingIssue:(NSData *)data   // 诊断模型加载问题
- (void)removeCurrentModel                         // 移除当前模型
```

### 材质系统方法
```objc
- (MaterialInstance*)createSimpleMaterial          // 创建简单材质
- (MaterialInstance*)loadCustomMaterial            // 加载自定义材质
- (MaterialInstance*)createRuntimeUnlitMaterial    // 创建运行时材质
- (BOOL)tryApplyTextureToMaterial:texture:         // 尝试应用纹理到材质
```

**功能说明：** 
- **格式支持**：完整支持 glTF 2.0 和 GLB 二进制格式
- **错误诊断**：详细的文件格式验证和错误诊断系统
- **材质管理**：支持预编译材质(.filamat)和默认材质，自动参数检测
- **资源管理**：自动纹理加载、材质实例管理和内存清理

---

## 🔧 6. 模型变换和几何处理

### 变换处理方法
```objc
- (void)standardizeModel                           // 标准化模型尺寸
- (void)transformToUnitCube                        // 将模型变换到单位立方体
- (void)centerModelAtOrigin                        // 将模型中心移到原点
- (void)moveModelToPosition:(math::float3)targetPosition // 移动模型到指定位置
```

### 几何计算方法
```objc
- (Aabb)calculateModelBounds                       // 计算模型边界框
- (void)verifyModelPosition                        // 验证模型位置
```

**功能说明：** 
- **标准化处理**：将不同尺寸的模型统一到标准大小和位置
- **边界框计算**：精确计算模型的世界坐标边界框
- **变换矩阵**：支持平移、缩放和复合变换
- **坐标系管理**：确保模型在场景中的正确定位

---

## 🌐 7. 内置几何体生成

### 几何体生成方法
```objc
- (BOOL)loadDefaultSphere                          // 生成默认球体
- (BOOL)updateSphereVertexColor:(math::float4)color // 更新球体顶点颜色
- (BOOL)simulateTextureWithColor:textureIndex:     // 用颜色模拟纹理效果
- (void)setMaterialColorForTextureIndex:material:  // 设置材质颜色
```

**功能说明：** 
- **程序化生成**：创建高质量球体几何（32段×16环）
- **顶点属性**：支持位置、切线、UV、颜色等完整顶点属性
- **动态着色**：实时更新顶点颜色，支持多纹理颜色映射
- **性能优化**：使用索引缓冲区减少顶点重复

---

## 🖼️ 8. 纹理管理系统

### 纹理操作方法
```objc
- (BOOL)loadTextureFromUIImage:(UIImage *)image    // 从UIImage加载纹理
- (void)clearTextures                              // 清空所有纹理
- (BOOL)selectTextureAtIndex:(NSInteger)index      // 选择纹理
- (NSInteger)getTextureCount                       // 获取纹理数量
- (void)updateMaterialTexture                      // 更新材质纹理
```

**功能说明：** 
- **格式转换**：UIImage → RGBA8 纹理格式转换
- **多纹理支持**：动态纹理列表管理和切换
- **自动应用**：智能检测材质参数并应用纹理
- **内存管理**：纹理资源的创建、存储和销毁

---

## 🎭 9. 纹理变换和动画

### 变换动画方法
```objc
- (void)moveTextureWithDeltaX:deltaY:              // 移动纹理位置
- (void)resetTexturePosition                       // 重置纹理位置
- (void)startTextureAnimationWithSpeed:(float)speed // 开始纹理动画
- (void)stopTextureAnimation                       // 停止纹理动画
```

**功能说明：** 
- **实时变换**：支持纹理UV坐标的实时偏移
- **手势交互**：响应用户拖拽手势进行纹理移动
- **自动动画**：60FPS定时器驱动的纹理动画效果
- **状态管理**：动画的启动、停止和重置功能

---

## 📊 技术规格

### 代码统计
- **总方法数：** 35+ 个公共方法
- **功能模块：** 9 大类别
- **代码行数：** ~1,616 行
- **注释覆盖：** 详细的中文注释和调试日志

### 支持特性
- ✅ **模型格式：** glTF 2.0、GLB 二进制格式
- ✅ **渲染技术：** PBR 物理渲染、实时光照
- ✅ **纹理系统：** 多纹理管理、动态切换、变换动画
- ✅ **几何处理：** 模型标准化、边界框计算、变换矩阵
- ✅ **iOS 集成：** Metal 后端、UIImage 支持、手势交互
- ✅ **性能优化：** 资源管理、内存清理、错误处理

### 架构优势
- **模块化设计：** 功能明确分类，便于维护和扩展
- **错误处理：** 完善的验证、诊断和异常处理机制
- **性能优化：** 智能内存管理和资源清理策略
- **扩展性强：** 支持自定义材质、几何体和渲染管线
- **iOS优化：** 专为 iOS Metal 后端优化的实现

---

## 🎯 使用建议

### 典型使用流程
1. **初始化：** `init` → `setupWithMetalLayer`
2. **加载模型：** `loadModelFromData` 或 `loadDefaultSphere`
3. **纹理管理：** `loadTextureFromUIImage` → `selectTextureAtIndex`
4. **渲染循环：** 持续调用 `render`
5. **交互控制：** `moveTextureWithDeltaX` 或 `startTextureAnimation`

### 扩展开发
- 新增几何体：参考 `loadDefaultSphere` 实现
- 自定义材质：通过 `.filamat` 文件或修改 `createSimpleMaterial`
- 动画效果：扩展 `startTextureAnimationWithSpeed` 支持更多动画类型

这个重新整理的架构为 iOS 3D 渲染应用提供了坚实的技术基础，具备良好的可维护性和扩展性。