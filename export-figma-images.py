#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Figma 图片批量导出脚本
从指定的 Figma 设计稿中导出所有节点的 2x 和 3x 分辨率图片
"""

import os
import base64
import json
import re
from pathlib import Path

# 从 Figma 代码中提取的节点 ID 列表
NODE_IDS = [
    "62-7276",  # 根节点
    "62-7277",  # top
    "62-7278",  # Status Bar
    "I62_7278-7_3263",  # Status Bar
    "I62_7278-7_3263-7022_135601",  # Right Side
    "I62_7278-7_3263-7022_135617",  # Left Side
    "I62_7278-7_3263-7022_135618",  # Time / Light / Base
    "62-7279",  # Navbar/Fixed
    "I62_7279-41_15228",  # Overrides/NavigationBar/Right
    "I62_7279-41_15228-6992_112988",  # Button
    "I62_7279-41_15228-6992_112988-81_9896",  # downloadBold
    "I62_7279-41_15228-6992_112988-81_9896-5084_3",  # Vector
    "I62_7279-41_15228-6992_112988-81_9897",  # 导出 text
    "I62_7279-41_15164",  # Overrides/NavigationBar/Middle
    "I62_7279-41_15165",  # Overrides/NavigationBar/Left
    "I62_7279-41_15165-13921_186685",  # chevronLeftBlack
    "I62_7279-41_15165-13921_186685-2_727",  # Vector
    "62-7280",  # area
    "62-7282",  # image 2
    "62-7283",  # Sidebar
    "I62_7283-12530_205438",
    "I62_7283-12530_205439",  # Overrides/Tool/Left
    "I62_7283-12530_205439-6109_118503",  # undoBlack
    "I62_7283-12530_205439-6109_118503-2631_47",  # Vector
    "I62_7283-12530_205439-6109_118504",  # redoBlack
    "I62_7283-12530_205439-6109_118504-2631_48",  # Vector
    "I62_7283-12530_205440",  # Overrides
    "I62_7283-12530_205440-7573_72889",  # Line 1 (Stroke)
    "I62_7283-12530_205440-7573_73319",  # Overrides/Progress Bar/Handle/White
    "I62_7283-12530_205440-7573_72890",  # Line
    "I62_7283-12530_205489",  # layerBold
    "I62_7283-12530_205489-2_978",  # Vector
    "62-7284",  # ToolBar/三级/抠图
    "62-7285",  # Titlebar/Tool
    "62-7286",
    "62-7288",  # SegmentedPicker
    "I62_7288-23079_202120",  # _SegmentedPicker-option
    "I62_7288-23079_202396",  # content
    "I62_7288-23079_202122",  # 自动 text
    "I62_7288-23079_202129",  # _SegmentedPicker-option
    "I62_7288-23079_202406",  # content
    "I62_7288-23079_202131",  # 手动 text
    "62-7289",  # slashCircleBold
    "I62_7289-2_650",  # Vector
    "72-6165",  # Tool/二级
    "72-6166",  # sliders
    "72-6167",  # Detail/CardList
    "I72_6167-62_8336",  # Title
    "I72_6167-62_8337",  # 大小 text
    "I72_6167-62_8350",  # Overrides/CardList/Right
    "I72_6167-62_8521",  # Slider
    "I72_6167-62_8521-62_7199",
    "I72_6167-62_8521-114_22458",  # ActiveLoad
    "I72_6167-62_8352",  # Input
    "I72_6167-62_8353",  # 7.0 text
]

def clean_node_id(node_id):
    """清理节点 ID，将下划线替换为冒号"""
    return node_id.replace('_', ':')

def save_base64_image(base64_data, file_path):
    """保存 base64 编码的图片数据到文件"""
    try:
        # 移除 data:image/png;base64, 前缀（如果存在）
        if base64_data.startswith('data:image/'):
            base64_data = base64_data.split(',')[1]
        
        # 解码 base64 数据
        image_data = base64.b64decode(base64_data)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 写入文件
        with open(file_path, 'wb') as f:
            f.write(image_data)
        
        print(f"✅ 保存图片: {file_path}")
        return True
    except Exception as e:
        print(f"❌ 保存图片失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始批量导出 Figma 图片...")
    
    export_dir = Path("/Users/<USER>/Projects/personal/Filamenet-1/export-images")
    export_dir.mkdir(exist_ok=True)
    
    # 统计信息
    total_nodes = len(NODE_IDS)
    success_count = 0
    failed_count = 0
    
    print(f"📊 总共需要导出 {total_nodes} 个节点，每个节点 2 种分辨率 (2x, 3x)")
    print(f"📊 预计生成 {total_nodes * 2} 张图片")
    
    for i, node_id in enumerate(NODE_IDS, 1):
        print(f"\n🔄 处理节点 {i}/{total_nodes}: {node_id}")
        
        # 清理节点 ID
        clean_id = clean_node_id(node_id)
        
        # 为每个分辨率导出图片
        for scale in ["2x", "3x"]:
            try:
                # 这里需要调用 MCP 工具获取图片
                # 由于这是 Python 脚本，我们需要在外部调用 MCP
                print(f"  📸 导出 {scale} 分辨率...")
                
                # 文件名格式：node-{clean_id}@{scale}.png
                filename = f"node-{clean_id.replace(':', '-')}@{scale}.png"
                file_path = export_dir / filename
                
                print(f"  💾 目标文件: {filename}")
                
                # 这里暂时创建占位符，实际需要通过 MCP 获取
                # success = save_base64_image(base64_data, str(file_path))
                # if success:
                #     success_count += 1
                # else:
                #     failed_count += 1
                
            except Exception as e:
                print(f"  ❌ 处理失败: {e}")
                failed_count += 1
    
    print(f"\n📈 导出完成!")
    print(f"✅ 成功: {success_count} 张")
    print(f"❌ 失败: {failed_count} 张")
    print(f"📁 输出目录: {export_dir}")

if __name__ == "__main__":
    main()