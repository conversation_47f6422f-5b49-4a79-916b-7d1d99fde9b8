#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保存 Figma 图片的辅助脚本
"""

import base64
import os
from pathlib import Path

def save_base64_image(base64_data, file_path):
    """保存 base64 编码的图片数据到文件"""
    try:
        # 移除 data:image/png;base64, 前缀（如果存在）
        if base64_data.startswith('data:image/'):
            base64_data = base64_data.split(',')[1]
        
        # 解码 base64 数据
        image_data = base64.b64decode(base64_data)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 写入文件
        with open(file_path, 'wb') as f:
            f.write(image_data)
        
        print(f"✅ 保存图片成功: {file_path}")
        print(f"📊 文件大小: {len(image_data)} bytes")
        return True
    except Exception as e:
        print(f"❌ 保存图片失败 {file_path}: {e}")
        return False

# 根节点的 base64 数据
root_node_base64 = "iVBORw0KGgoAAAANSUhEUgAAAXcAAAMsCAYAAACvFuLjAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUABXPiSURBVHgB7P1nkG1Zdh6IrWOuN+kzn/fvlXdd1RZAdzUIgCJBEk2ImhlaADOiImYUMwAUIRMx0hCUCYn6Q0BSjBhBSQA1YsxIZADgUPRAGwCNalNd3pvn86W31997zHzf2nufezLbd1ejG2/Ors7OfJn3nnvONmt961vOE5FUilGMYhSjGPfV8KUYxShGMYpx341CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IejEO7FKEYxinEfjkK4F6MYxSjGfTgK4V6MYhSjGPfhKIR7MYpRjGLch6MQ7sUoRjGKcR+OQrgXoxjFKMZ9OArhXoxiFKMY9+EohHsxilGMYtyHoxDuxShGMYpxH45CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IcjlB+h8Xf+zt+RZ599VmZnZ+XmzZvyd//u35WXXnrpO37/hQsX9Boc/+yf/TP53d/93SN/57V/4Rd+Qfb39+VXf/VXpRjFKMb9NZz8oNygDPlWg6978skn5ed+7uf0Zzdefvll+fznP/8dy55Lly7J8vKyfBBjc3NTrl+/Lh/E8ORHpBPTb/7mb8ov/uIvquDlpHKROPi7f/SP/tF3dI0bN26ogOegYvi1X/u17G9//+//ffmVX/kV/ZmLfvHiRSlGMYpx/4zPfOYz8ju/8zv682/91m/JL/3SL33D11GQ//Iv/7LKg7xQPz4cwOS1vtl49NFH5bHHHpMPcrzwwgvy9ttvy/c7fiSQOxeFQpxC/dOf/rQKeArpF198UX79139dUTh/960GETvfw4XgtfKDC87P+I3f+A1d1GIUoxj33/hWgtoNgkbKg/xrKXccSndonrKEXwSdtPapKL6RJfDggw/q909+8pPy0EMPyfczvva1r8nzzz+vCuODEO4c6Q/7Cwg75YAAPvJ7CHb9PYS1/huTnWKyv+51/D0H/4bF0595Tfd3LKb+nj9zAOH/0J+5+Cq+iq/v74vnOH+eKSfcoCw4/nonJzj29vZURkCYf8NrQ8CnAIrZ6wE0v+Hr/upf/av69UGMXq+XXe+DmJ8fKYeqo1TcmJmZ0e/UpBxE5PxyvLob1MTOhPpG4y//5b+sHFoxilGM+2c4eXFcbnyz4WQEvz/11FNK25K15z73OaVwyRRQ1jjE/s0ong9y1Ov1I//++Mc/Lt/P+JGgZSh4KbBJmXAiaSLRFHL0ihPypFycs9UNvoYL5BbhO13oYhSjGH96Bs845QOFLgX0t6Npv9mgQCfVSzlCQe58e25QfvB35OP52m/Huf8gB7n8W7duyb179+R7GT8ywp2TSAHvHCIuooVa9ODgQH9H4e2cohwuOoa//2EtQDGKUYwf/OC5J+DjYDTL93PeHSp3gp3yx/n1XPQMZQs/k8Ec3y7q5kd1/MiEQlJLcsE4udSqDGOkE5Tjm00uF8c5UV1kzPnz5/U7F4g//0mYU8UoRjE+uJEPaWYQxHcTDv2dDofeeW0XMs1/U5Y41M7f/zAF+zvvvPM9o3aOHwnh7uLPv/CFLxzRyC6yJR+vzoV3E+4omOPRMRxUEo6uKUYxivGnZ/Dc5s/0D+oMO0BIReLCIilbyBjkw6i/k9Hv97+OM/9+x/frJ/yREO7UnqRjuKBPPPGEml1E3lxkCnsnzPl3Oj/40AyZ5AIcXwQqCnJpx+Pci1GMYvzpGN9JSOMHOTzPyz7zuE/v243JZCKlUkn+wT/4B/L000/L9zPefPNN/T4ej+WDGD8Swp1cF73XFMp5Tp2IPZ9J6pwo36szpRjFKMaP/sgLV+dvy4+8HMgL42/3vm828gieoPG7oYFeeeUVFeqMT+fXBzFIx3wQ40cmQ9UNx7lzgr+REOffCuFejGLc34P+Nud7c0mNlA382dEVLrIln4R0/H3fzeD1vxd+n9nuKysr8kGMjY0NDcn8IMaPnHAvRjGKUYxifP+jqApZjGIUoxj34SiEezGKUYxi3IejEO7FKEYxinEfjkK4F6MYxSjGfTgK4V6MYhSjGPfhKIR7MYpRjGLch6MQ7sUoRjGKcR+OQrgXoxjFKMZ9OArhXoxiFKMY9+EohHsxilGMYtyHoxDuxShGMYpxH45CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IejEO7FKEYxinEfjkK4F6MYxSjGfTgK4V6MYhSjGPfhKIR7MYpRjGLch6MQ7sUoRjGKcR+OQrgXoxjFKMZ9OArhXoxiFKMY9+EohHsxilGMYtyHoxDuxShGMYpxH45CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IcjlB+h8Xf+zt+RZ599VmZnZ+XmzZvyd//u35WXXnrpO37/hQsX9Boc/+yf/TP53d/93SN/57V/4Rd+Qfb39+VXf/VXpRjFKMb9NZz8oNygDPlWg6978skn5ed+7uf0Zzdefvll+fznP/8dy55Lly7J8vKyfBBjc3NTrl+/Lh/E8ORHpBPTb/7mb8ov/uIvquDlpHKROPi7f/SP/tF3dI0bN26ogOegYvi1X/u17G9//+//ffmVX/kV/ZmLfvHiRSlGMYpx/4zPfOYz8ju/8zv682/91m/JL/3SL33D11GQ//Iv/7LKg7xQPz4cwOS1vtl49NFH5bHHHpMPcrzwwgvy9ttvy/c7fiSQOxeFQpxC/dOf/rQKeArpF198UX79139dUTh/960GETvfw4XgtfKDC87P+I3f+A1d1GIUoxj33/hWgtoNgkbKg/xrKXccSndonrKEXwSdtPapKL6RJfDggw/q909+8pPy0EMPyfczvva1r8nzzz+vCuODEO4c6Q/7Cwg75YAAPvJ7CHb9PYS1/huTnWKyv+51/D0H/4bF0595Tfd3LKb+nj9zAOH/0J+5+Cq+iq/v74vnOH+eKSfcoCw4/nonJzj29vZURkCYf8NrQ8CnAIrZ6wE0v+Hr/upf/av69UGMXq+XXe+DmJ8fKYeqo1TcmJmZ0e/UpBxE5PxyvLob1MTOhPpG4y//5b+sHFoxilGM+2c4eXFcbnyz4WQEvz/11FNK25K15z73OaVwyRRQ1jjE/s0ong9y1Ov1I//++Mc/Lt/P+JGgZSh4KbBJmXAiaSLRFHL0ihPypFycs9UNvoYL5BbhO13oYhSjGH96Bs845QOFLgX0t6Npv9mgQCfVSzlCQe58e25QfvB35OP52m/Huf8gB7n8W7duyb179+R7GT8ywp2TSAHvHCIuooVa9ODgQH9H4e2cohwuOoa//2EtQDGKUYwf/OC5J+DjYDTL93PeHSp3gp3yx/n1XPQMZQs/k8Ec3y7q5kd1/MiEQlJLcsE4udSqDGOkE5Tjm00uF8c5UV1kzPnz5/U7F4g//0mYU8UoRjE+uJEPaWYQxHcTDv2dDofeeW0XMs1/U5Y41M7f/zAF+zvvvPM9o3aOHwnh7uLPv/CFLxzRyC6yJR+vzoV3E+4omOPRMRxUEo6uKUYxivGnZ/Dc5s/0D+oMO0BIReLCIilbyBjkw6i/k9Hv97+OM/9+x/frJ/yREO7UnqRjuKBPPPGEml1E3lxkCnsnzPl3Oj/40AyZ5AIcXwQqCnJpx+Pci1GMYvzpGN9JSOMHOTzPyz7zuE/v243JZCKlUkn+wT/4B/L000/L9zPefPNN/T4ej+WDGD8Swp1cF73XFMp5Tp2IPZ9J6pwo36szpRjFKMaP/sgLV+dvy4+8HMgL42/3vm828gieoPG7oYFeeeUVFeqMT+fXBzFIx3wQ40cmQ9UNx7lzgr+REOffCuFejGLc34P+Nud7c0mNlA382dEVLrIln4R0/H3fzeD1vxd+n9nuKysr8kGMjY0NDcn8IMaPnHAvRjGKUYxifP+jqApZjGIUoxj34SiEezGKUYxi3IejEO7FKEYxinEfjkK4F6MYxSjGfTgK4V6MYhSjGPfhKIR7MYpRjGLch6MQ7sUoRjGKcR+OQrgXoxjFKMZ9OArhXoxiFKMY9+EohHsxilGMYtyHoxDuxShGMYpxH45CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IejEO7FKEYxinEfjkK4F6MYxSjGfTgK4V6MYhSjGPfhKIR7MYpRjGLch6MQ7sUoRjGKcR+OQrgXoxjFKMZ9OArhXoxiFKMY9+EohHsxilGMYtyHoxDuxShGMYpxH45CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IcjlB+h8Xf+zt+RZ599VmZnZ+XmzZvyd//u35WXXnrpO37/hQsX9Boc/+yf/TP53d/93SN/57V/4Rd+Qfb39+VXf/VXpRjFKMb9NZz8oNygDPlWg6978skn5ed+7uf0Zzdefvll+fznP/8dy55Lly7J8vKyfBBjc3NTrl+/Lh/E8ORHpBPTb/7mb8ov/uIvquDlpHKROPi7f/SP/tF3dI0bN26ogOegYvi1X/u17G9//+//ffmVX/kV/ZmLfvHiRSlGMYpx/4zPfOYz8ju/8zv682/91m/JL/3SL33D11GQ//Iv/7LKg7xQPz4cwOS1vtl49NFH5bHHHpMPcrzwwgvy9ttvy/c7fiSQOxeFQpxC/dOf/rQKeArpF198UX79139dUTh/960GETvfw4XgtfKDC87P+I3f+A1d1GIUoxj33/hWgtoNgkbKg/xrKXccSndonrKEXwSdtPapKL6RJfDggw/q909+8pPy0EMPyfczvva1r8nzzz+vCuODEO4c6Q/7Cwg75YAAPvJ7CHb9PYS1/huTnWKyv+51/D0H/4bF0595Tfd3LKb+nj9zAOH/0J+5+Cq+iq/v74vnOH+eKSfcoCw4/ronJzj29vZURkCYf8NrQ8CnAIrZ6wE0v+Hr/upf/av69UGMXq+XXe+DmJ8fKYeqo1TcmJmZ0e/UpBxE5PxyvLob1MTOhPpG4y//5b+sHFoxilGM+2c4eXFcbnyz4WQEvz/11FNK25K15z73OaVwyRRQ1jjE/s0ong9y1Ov1I//++Mc/Lt/P+JGgZSh4KbBJmXAiaSLRFHL0ihPypFycs9UNvoYL5BbhO13oYhSjGH96Bs845QOFLgX0t6Npv9mgQCfVSzlCQe58e25QfvB35OP52m/Huf8gB7n8W7duyb179+R7GT8ywp2TSAHvHCIuooVa9ODgQH9H4e2cohwuOoa//2EtQDGKUYwf/OC5J+DjYDTL93PeHSp3gp3yx/n1XPQMZQs/k8Ec3y7q5kd1/MiEQlJLcsE4udSqDGOkE5Tjm00uF8c5UV1kzPnz5/U7F4g//0mYU8UoRjE+uJEPaWYQxHcTDv2dDofeeW0XMs1/U5Y41M7f/zAF+zvvvPM9o3aOHwnh7uLPv/CFLxzRyC6yJR+vzoV3E+4omOPRMRxUEo6uKUYxivGnZ/Dc5s/0D+oMO0BIReLCIilbyBjkw6i/k9Hv97+OM/9+x/frJ/yREO7UnqRjuKBPPPGEml1E3lxkCnsnzPl3Oj/40AyZ5AIcXwQqCnJpx+Pci1GMYvzpGN9JSOMHOTzPyz7zuE/v243JZCKlUkn+wT/4B/L000/L9zPefPNN/T4ej+WDGD8Swp1cF73XFMp5Tp2IPZ9J6pwo36szpRjFKMaP/sgLV+dvy4+8HMgL42/3vm828gieoPG7oYFeeeUVFeqMT+fXBzFIx3wQ40cmQ9UNx7lzgr+REOffCuFejGLc34P+Nud7c0mNlA382dEVLrIln4R0/H3fzeD1vxd+n9nuKysr8kGMjY0NDcn8IMaPnHAvRjGKUYxifP+jqApZjGIUoxj34SiEezGKUYxi3IejEO7FKEYxinEfjkK4F6MYxSjGfTgK4V6MYhSjGPfhKIR7MYpRjGLch6MQ7sUoRjGKcR+OQrgXoxjFKMZ9OArhXoxiFKMY9+EohHsxilGMYtyHoxDuxShGMYpxH45CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IejEO7FKEYxinEfjkK4F6MYxSjGfTgK4V6MYhSjGPfhKIR7MYpRjGLch6MQ7sUoRjGKcR+OQrgXoxjFKMZ9OArhXoxiFKMY9+EohHsxilGMYtyHoxDuxShGMYpxH45CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IcjlB+h8Xf+zt+RZ599VmZnZ+XmzZvyd//u35WXXnrpO37/hQsX9Boc/+yf/TP53d/93SN/57V/4Rd+Qfb39+VXf/VXpRjFKMb9NZz8oNygDPlWg6978skn5ed+7uf0Zzdefvll+fznP/8dy55Lly7J8vKyfBBjc3NTrl+/Lh/E8ORHpBPTb/7mb8ov/uIvquDlpHKROPi7f/SP/tF3dI0bN26ogOegYvi1X/u17G9//+//ffmVX/kV/ZmLfvHiRSlGMYpx/4zPfOYz8ju/8zv682/91m/JL/3SL33D11GQ//Iv/7LKg7xQPz4cwOS1vtl49NFH5bHHHpMPcrzwwgvy9ttvy/c7fiSQOxeFQpxC/dOf/rQKeArpF198UX79139dUTh/960GETvfw4XgtfKDC87P+I3f+A1d1GIUoxj33/hWgtoNgkbKg/xrKXccSndonrKEXwSdtPapKL6RJfDggw/q909+8pPy0EMPyfczvva1r8nzzz+vCuODEO4c6Q/7Cwg75YAAPvJ7CHb9PYS1/huTnWKyv+51/D0H/4bF0595Tfd3LKb+nj9zAOH/0J+5+Cq+iq/v74vnOH+eKSfcoCw4/ronJzj29vZURkCYf8NrQ8CnAIrZ6wE0v+Hr/upf/av69UGMXq+XXe+DmJ8fKYeqo1TcmJmZ0e/UpBxE5PxyvLob1MTOhPpG4y//5b+sHFoxilGM+2c4eXFcbnyz4WQEvz/11FNK25K15z73OaVwyRRQ1jjE/s0ong9y1Ov1I//++Mc/Lt/P+JGgZSh4KbBJmXAiaSLRFHL0ihPypFycs9UNvoYL5BbhO13oYhSjGH96Bs845QOFLgX0t6Npv9mgQCfVSzlCQe58e25QfvB35OP52m/Huf8gB7n8W7duyb179+R7GT8ywp2TSAHvHCIuooVa9ODgQH9H4e2cohwuOoa//2EtQDGKUYwf/OC5J+DjYDTL93PeHSp3gp3yx/n1XPQMZQs/k8Ec3y7q5kd1/MiEQlJLcsE4udSqDGOkE5Tjm00uF8c5UV1kzPnz5/U7F4g//0mYU8UoRjE+uJEPaWYQxHcTDv2dDofeeW0XMs1/U5Y41M7f/zAF+zvvvPM9o3aOHwnh7uLPv/CFLxzRyC6yJR+vzoV3E+4omOPRMRxUEo6uKUYxivGnZ/Dc5s/0D+oMO0BIReLCIilbyBjkw6i/k9Hv97+OM/9+x/frJ/yREO7UnqRjuKBPPPGEml1E3lxkCnsnzPl3Oj/40AyZ5AIcXwQqCnJpx+Pci1GMYvzpGN9JSOMHOTzPyz7zuE/v243JZCKlUkn+wT/4B/L000/L9zPefPNN/T4ej+WDGD8Swp1cF73XFMp5Tp2IPZ9J6pwo36szpRjFKMaP/sgLV+dvy4+8HMgL42/3vm828gieoPG7oYFeeeUVFeqMT+fXBzFIx3wQ40cmQ9UNx7lzgr+REOffCuFejGLc34P+Nud7c0mNlA382dEVLrIln4R0/H3fzeD1vxd+n9nuKysr8kGMjY0NDcn8IMaPnHAvRjGKUYxifP+jqApZjGIUoxj34SiEezGKUYxi3IejEO7FKEYxinEfjkK4F6MYxSjGfTgK4V6MYhSjGPfhKIR7MYpRjGLch6MQ7sUoRjGKcR+OQrgXoxjFKMZ9OArhXoxiFKMY9+EohHsxilGMYtyHoxDuxShGMYpxH45CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IejEO7FKEYxinEfjkK4F6MYxSjGfTgK4V6MYhSjGPfhKIR7MYpRjGLch6MQ7sUoRjGKcR+OQrgXoxjFKMZ9OArhXoxiFKMY9+EohHsxilGMYtyHoxDuxShGMYpxH45CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IcjlB+h8Xf+zt+RZ599VmZnZ+XmzZvyd//u35WXXnrpO37/hQsX9Boc/+yf/TP53d/93SN/57V/4Rd+Qfb39+VXf/VXpRjFKMb9NZz8oNygDPlWg6978skn5ed+7uf0Zzdefvll+fznP/8dy55Lly7J8vKyfBBjc3NTrl+/Lh/E8ORHpBPTb/7mb8ov/uIvquDlpHKROPi7f/SP/tF3dI0bN26ogOegYvi1X/u17G9//+//ffmVX/kV/ZmLfvHiRSlGMYpx/4zPfOYz8ju/8zv682/91m/JL/3SL33D11GQ//Iv/7LKg7xQPz4cwOS1vtl49NFH5bHHHpMPcrzwwgvy9ttvy/c7fiSQOxeFQpxC/dOf/rQKeArpF198UX79139dUTh/960GETvfw4XgtfKDC87P+I3f+A1d1GIUoxj33/hWgtoNgkbKg/xrKXccSndonrKEXwSdtPapKL6RJfDggw/q909+8pPy0EMPyfczvva1r8nzzz+vCuODEO4c6Q/7Cwg75YAAPvJ7CHb9PYS1/huTnWKyv+51/D0H/4bF0595Tfd3LKb+nj9zAOH/0J+5+Cq+iq/v74vnOH+eKSfcoCw4/ronJzj29vZURkCYf8NrQ8CnAIrZ6wE0v+Hr/upf/av69UGMXq+XXe+DmJ8fKYeqo1TcmJmZ0e/UpBxE5PxyvLob1MTOhPpG4y//5b+sHFoxilGM+2c4eXFcbnyz4WQEvz/11FNK25K15z73OaVwyRRQ1jjE/s0ong9y1Ov1I//++Mc/Lt/P+JGgZSh4KbBJmXAiaSLRFHL0ihPypFycs9UNvoYL5BbhO13oYhSjGH96Bs845QOFLgX0t6Npv9mgQCfVSzlCQe58e25QfvB35OP52m/Huf8gB7n8W7duyb179+R7GT8ywp2TSAHvHCIuooVa9ODgQH9H4e2cohwuOoa//2EtQDGKUYwf/OC5J+DjYDTL93PeHSp3gp3yx/n1XPQMZQs/k8Ec3y7q5kd1/MiEQlJLcsE4udSqDGOkE5Tjm00uF8c5UV1kzPnz5/U7F4g//0mYU8UoRjE+uJEPaWYQxHcTDv2dDofeeW0XMs1/U5Y41M7f/zAF+zvvvPM9o3aOHwnh7uLPv/CFLxzRyC6yJR+vzoV3E+4omOPRMRxUEo6uKUYxivGnZ/Dc5s/0D+oMO0BIReLCIilbyBjkw6i/k9Hv97+OM/9+x/frJ/yREO7UnqRjuKBPPPGEml1E3lxkCnsnzPl3Oj/40AyZ5AIcXwQqCnJpx+Pci1GMYvzpGN9JSOMHOTzPyz7zuE/v243JZCKlUkn+wT/4B/L000/L9zPefPNN/T4ej+WDGD8Swp1cF73XFMp5Tp2IPZ9J6pwo36szpRjFKMaP/sgLV+dvy4+8HMgL42/3vm828gieoPG7oYFeeeUVFeqMT+fXBzFIx3wQ40cmQ9UNx7lzgr+REOffCuFejGLc34P+Nud7c0mNlA382dEVLrIln4R0/H3fzeD1vxd+n9nuKysr8kGMjY0NDcn8IMaPnHAvRjGKUYxifP+jqApZjGIUoxj34SiEezGKUYxi3IejEO7FKEYxinEfjkK4F6MYxSjGfTgK4V6MYhSjGPfhKIR7MYpRjGLch6MQ7sUoRjGKcR+OQrgXoxjFKMZ9OArhXoxiFKMY9+EohHsxilGMYtyHoxDuxShGMYpxH45CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IejEO7FKEYxinEfjkK4F6MYxSjGfTgK4V6MYhSjGPfhKIR7MYpRjGLch6MQ7sUoRjGKcR+OQrgXoxjFKMZ9OArhXoxiFKMY9+EohHsxilGMYtyHoxDuxShGMYpxH45CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IcjlB+h8Xf+zt+RZ599VmZnZ+XmzZvyd//u35WXXnrpO37/hQsX9Boc/+yf/TP53d/93SN/57V/4Rd+Qfb39+VXf/VXpRjFKMb9NZz8oNygDPlWg6978skn5ed+7uf0Zzdefvll+fznP/8dy55Lly7J8vKyfBBjc3NTrl+/Lh/E8ORHpBPTb/7mb8ov/uIvquDlpHKROPi7f/SP/tF3dI0bN26ogOegYvi1X/u17G9//+//ffmVX/kV/ZmLfvHiRSlGMYpx/4zPfOYz8ju/8zv682/91m/JL/3SL33D11GQ//Iv/7LKg7xQPz4cwOS1vtl49NFH5bHHHpMPcrzwwgvy9ttvy/c7fiSQOxeFQpxC/dOf/rQKeArpF198UX79139dUTh/960GETvfw4XgtfKDC87P+I3f+A1d1GIUoxj33/hWgtoNgkbKg/xrKXccSndonrKEXwSdtPapKL6RJfDggw/q909+8pPy0EMPyfczvva1r8nzzz+vCuODEO4c6Q/7Cwg75YAAPvJ7CHb9PYS1/huTnWKyv+51/D0H/4bF0595Tfd3LKb+nj9zAOH/0J+5+Cq+iq/v74vnOH+eKSfcoCw4/ronJzj29vZURkCYf8NrQ8CnAIrZ6wE0v+Hr/upf/av69UGMXq+XXe+DmJ8fKYeqo1TcmJmZ0e/UpBxE5PxyvLob1MTOhPpG4y//5b+sHFoxilGM+2c4eXFcbnyz4WQEvz/11FNK25K15z73OaVwyRRQ1jjE/s0ong9y1Ov1I//++Mc/Lt/P+JGgZSh4KbBJmXAiaSLRFHL0ihPypFycs9UNvoYL5BbhO13oYhSjGH96Bs845QOFLgX0t6Npv9mgQCfVSzlCQe58e25QfvB35OP52m/Huf8gB7n8W7duyb179+R7GT8ywp2TSAHvHCIuooVa9ODgQH9H4e2cohwuOoa//2EtQDGKUYwf/OC5J+DjYDTL93PeHSp3gp3yx/n1XPQMZQs/k8Ec3y7q5kd1/MiEQlJLcsE4udSqDGOkE5Tjm00uF8c5UV1kzPnz5/U7F4g//0mYU8UoRjE+uJEPaWYQxHcTDv2dDofeeW0XMs1/U5Y41M7f/zAF+zvvvPM9o3aOHwnh7uLPv/CFLxzRyC6yJR+vzoV3E+4omOPRMRxUEo6uKUYxivGnZ/Dc5s/0D+oMO0BIReLCIilbyBjkw6i/k9Hv97+OM/9+x/frJ/yREO7UnqRjuKBPPPGEml1E3lxkCnsnzPl3Oj/40AyZ5AIcXwQqCnJpx+Pci1GMYvzpGN9JSOMHOTzPyz7zuE/v243JZCKlUkn+wT/4B/L000/L9zPefPNN/T4ej+WDGD8Swp1cF73XFMp5Tp2IPZ9J6pwo36szpRjFKMaP/sgLV+dvy4+8HMgL42/3vm828gieoPG7oYFeeeUVFeqMT+fXBzFIx3wQ40cmQ9UNx7lzgr+REOffCuFejGLc34P+Nud7c0mNlA382dEVLrIln4R0/H3fzeD1vxd+n9nuKysr8kGMjY0NDcn8IMaPnHAvRjGKUYxifP+jqApZjGIUoxj34SiEezGKUYxi3IejEO7FKEYxinEfjkK4F6MYxSjGfTgK4V6MYhSjGPfhKIR7MYpRjGLch6MQ7sUoRjGKcR+OQrgXoxjFKMZ9OArhXoxiFKMY9+EohHsxilGMYtyHoxDuxShGMYpxH45CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IejEO7FKEYxinEfjkK4F6MYxSjGfTgK4V6MYhSjGPfhKIR7MYpRjGLch6MQ7sUoRjGKcR+OQrgXoxjFKMZ9OArhXoxiFKMY9+EohHsxilGMYtyHoxDuxShGMYpxH45CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IcjlB+h8Xf+zt+RZ599VmZnZ+XmzZvyd//u35WXXnrpO37/hQsX9Boc/+yf/TP53d/93SN/57V/4Rd+Qfb39+VXf/VXpRjFKMb9NZz8oNygDPlWg6978skn5ed+7uf0Zzdefvll+fznP/8dy55Lly7J8vKyfBBjc3NTrl+/Lh/E8ORHpBPTb/7mb8ov/uIvquDlpHKROPi7f/SP/tF3dI0bN26ogOegYvi1X/u17G9//+//ffmVX/kV/ZmLfvHiRSlGMYpx/4zPfOYz8ju/8zv682/91m/JL/3SL33D11GQ//Iv/7LKg7xQPz4cwOS1vtl49NFH5bHHHpMPcrzwwgvy9ttvy/c7fiSQOxeFQpxC/dOf/rQKeArpF198UX79139dUTh/960GETvfw4XgtfKDC87P+I3f+A1d1GIUoxj33/hWgtoNgkbKg/xrKXccSndonrKEXwSdtPapKL6RJfDggw/q909+8pPy0EMPyfczvva1r8nzzz+vCuODEO4c6Q/7Cwg75YAAPvJ7CHb9PYS1/huTnWKyv+51/D0H/4bF0595Tfd3LKb+nj9zAOH/0J+5+Cq+iq/v74vnOH+eKSfcoCw4/ronJzj29vZURkCYf8NrQ8CnAIrZ6wE0v+Hr/upf/av69UGMXq+XXe+DmJ8fKYeqo1TcmJmZ0e/UpBxE5PxyvLob1MTOhPpG4y//5b+sHFoxilGM+2c4eXFcbnyz4WQEvz/11FNK25K15z73OaVwyRRQ1jjE/s0ong9y1Ov1I//++Mc/Lt/P+JGgZSh4KbBJmXAiaSLRFHL0ihPypFycs9UNvoYL5BbhO13oYhSjGH96Bs845QOFLgX0t6Npv9mgQCfVSzlCQe58e25QfvB35OP52m/Huf8gB7n8W7duyb179+R7GT8ywp2TSAHvHCIuooVa9ODgQH9H4e2cohwuOoa//2EtQDGKUYwf/OC5J+DjYDTL93PeHSp3gp3yx/n1XPQMZQs/k8Ec3y7q5kd1/MiEQlJLcsE4udSqDGOkE5Tjm00uF8c5UV1kzPnz5/U7F4g//0mYU8UoRjE+uJEPaWYQxHcTDv2dDofeeW0XMs1/U5Y41M7f/zAF+zvvvPM9o3aOHwnh7uLPv/CFLxzRyC6yJR+vzoV3E+4omOPRMRxUEo6uKUYxivGnZ/Dc5s/0D+oMO0BIReLCIilbyBjkw6i/k9Hv97+OM/9+x/frJ/yREO7UnqRjuKBPPPGEml1E3lxkCnsnzPl3Oj/40AyZ5AIcXwQqCnJpx+Pci1GMYvzpGN9JSOMHOTzPyz7zuE/v243JZCKlUkn+wT/4B/L000/L9zPefPNN/T4ej+WDGD8Swp1cF73XFMp5Tp2IPZ9J6pwo36szpRjFKMaP/sgLV+dvy4+8HMgL42/3vm828gieoPG7oYFeeeUVFeqMT+fXBzFIx3wQ40cmQ9UNx7lzgr+REOffCuFejGLc34P+Nud7c0mNlA382dEVLrIln4R0/H3fzeD1vxd+n9nuKysr8kGMjY0NDcn8IMaPnHAvRjGKUYxifP+jqApZjGIUoxj34SiEezGKUYxi3IejEO7FKEYxinEfjkK4F6MYxSjGfTgK4V6MYhSjGPfhKIR7MYpRjGLch6MQ7sUoRjGKcR+OQrgXoxjFKMZ9OArhXoxiFKMY9+EohHsxilGMYtyHoxDuxShGMYpxH45CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IejEO7FKEYxinEfjkK4F6MYxSjGfTgK4V6MYhSjGPfhKIR7MYpRjGLch6MQ7sUoRjGKcR+OQrgXoxjFKMZ9OArhXoxiFKMY9+EohHsxilGMYtyHoxDuxShGMYpxH45CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IcjlB+h8Xf+zt+RZ599VmZnZ+XmzZvyd//u35WXXnrpO37/hQsX9Boc/+yf/TP53d/93SN/57V/4Rd+Qfb39+VXf/VXpRjFKMb9NZz8oNygDPlWg6978skn5ed+7uf0Zzdefvll+fznP/8dy55Lly7J8vKyfBBjc3NTrl+/Lh/E8ORHpBPTb/7mb8ov/uIvquDlpHKROPi7f/SP/tF3dI0bN26ogOegYvi1X/u17G9//+//ffmVX/kV/ZmLfvHiRSlGMYpx/4zPfOYz8ju/8zv682/91m/JL/3SL33D11GQ//Iv/7LKg7xQPz4cwOS1vtl49NFH5bHHHpMPcrzwwgvy9ttvy/c7fiSQOxeFQpxC/dOf/rQKeArpF198UX79139dUTh/960GETvfw4XgtfKDC87P+I3f+A1d1GIUoxj33/hWgtoNgkbKg/xrKXccSndonrKEXwSdtPapKL6RJfDggw/q909+8pPy0EMPyfczvva1r8nzzz+vCuODEO4c6Q/7Cwg75YAAPvJ7CHb9PYS1/huTnWKyv+51/D0H/4bF0595Tfd3LKb+nj9zAOH/0J+5+Cq+iq/v74vnOH+eKSfcoCw4/ronJzj29vZURkCYf8NrQ8CnAIrZ6wE0v+Hr/upf/av69UGMXq+XXe+DmJ8fKYeqo1TcmJmZ0e/UpBxE5PxyvLob1MTOhPpG4y//5b+sHFoxilGM+2c4eXFcbnyz4WQEvz/11FNK25K15z73OaVwyRRQ1jjE/s0ong9y1Ov1I//++Mc/Lt/P+JGgZSh4KbBJmXAiaSLRFHL0ihPypFycs9UNvoYL5BbhO13oYhSjGH96Bs845QOFLgX0t6Npv9mgQCfVSzlCQe58e25QfvB35OP52m/Huf8gB7n8W7duyb179+R7GT8ywp2TSAHvHCIuooVa9ODgQH9H4e2cohwuOoa//2EtQDGKUYwf/OC5J+DjYDTL93PeHSp3gp3yx/n1XPQMZQs/k8Ec3y7q5kd1/MiEQlJLcsE4udSqDGOkE5Tjm00uF8c5UV1kzPnz5/U7F4g//0mYU8UoRjE+uJEPaWYQxHcTDv2dDofeeW0XMs1/U5Y41M7f/zAF+zvvvPM9o3aOHwnh7uLPv/CFLxzRyC6yJR+vzoV3E+4omOPRMRxUEo6uKUYxivGnZ/Dc5s/0D+oMO0BIReLCIilbyBjkw6i/k9Hv97+OM/9+x/frJ/yREO7UnqRjuKBPPPGEml1E3lxkCnsnzPl3Oj/40AyZ5AIcXwQqCnJpx+Pci1GMYvzpGN9JSOMHOTzPyz7zuE/v243JZCKlUkn+wT/4B/L000/L9zPefPNN/T4ej+WDGD8Swp1cF73XFMp5Tp2IPZ9J6pwo36szpRjFKMaP/sgLV+dvy4+8HMgL42/3vm828gieoPG7oYFeeeUVFeqMT+fXBzFIx3wQ40cmQ9UNx7lzgr+REOffCuFejGLc34P+Nud7c0mNlA382dEVLrIln4R0/H3fzeD1vxd+n9nuKysr8kGMjY0NDcn8IMaPnHAvRjGKUYxifP+jqApZjGIUoxj34SiEezGKUYxi3IejEO7FKEYxinEfjkK4F6MYxSjGfTgK4V6MYhSjGPfhKIR7MYpRjGLch6MQ7sUoRjGKcR+OQrgXoxjFKMZ9OArhXoxiFKMY9+EohHsxilGMYtyHoxDuxShGMYpxH45CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IejEO7FKEYxinEfjkK4F6MYxSjGfTgK4V6MYhSjGPfhKIR7MYpRjGLch6MQ7sUoRjGKcR+OQrgXoxjFKMZ9OArhXoxiFKMY9+EohHsxilGMYtyHoxDuxShGMYpxH45CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IcjlB+h8Xf+zt+RZ599VmZnZ+XmzZvyd//u35WXXnrpO37/hQsX9Boc/+yf/TP53d/93SN/57V/4Rd+Qfb39+VXf/VXpRjFKMb9NZz8oNygDPlWg6978skn5ed+7uf0Zzdefvll+fznP/8dy55Lly7J8vKyfBBjc3NTrl+/Lh/E8ORHpBPTb/7mb8ov/uIvquDlpHKROPi7f/SP/tF3dI0bN26ogOegYvi1X/u17G9//+//ffmVX/kV/ZmLfvHiRSlGMYpx/4zPfOYz8ju/8zv682/91m/JL/3SL33D11GQ//Iv/7LKg7xQPz4cwOS1vtl49NFH5bHHHpMPcrzwwgvy9ttvy/c7fiSQOxeFQpxC/dOf/rQKeArpF198UX79139dUTh/960GETvfw4XgtfKDC87P+I3f+A1d1GIUoxj33/hWgtoNgkbKg/xrKXccSndonrKEXwSdtPapKL6RJfDggw/q909+8pPy0EMPyfczvva1r8nzzz+vCuODEO4c6Q/7Cwg75YAAPvJ7CHb9PYS1/huTnWKyv+51/D0H/4bF0595Tfd3LKb+nj9zAOH/0J+5+Cq+iq/v74vnOH+eKSfcoCw4/ronJzj29vZURkCYf8NrQ8CnAIrZ6wE0v+Hr/upf/av69UGMXq+XXe+DmJ8fKYeqo1TcmJmZ0e/UpBxE5PxyvLob1MTOhPpG4y//5b+sHFoxilGM+2c4eXFcbnyz4WQEvz/11FNK25K15z73OaVwyRRQ1jjE/s0ong9y1Ov1I//++Mc/Lt/P+JGgZSh4KbBJmXAiaSLRFHL0ihPypFycs9UNvoYL5BbhO13oYhSjGH96Bs845QOFLgX0t6Npv9mgQCfVSzlCQe58e25QfvB35OP52m/Huf8gB7n8W7duyb179+R7GT8ywp2TSAHvHCIuooVa9ODgQH9H4e2cohwuOoa//2EtQDGKUYwf/OC5J+DjYDTL93PeHSp3gp3yx/n1XPQMZQs/k8Ec3y7q5kd1/MiEQlJLcsE4udSqDGOkE5Tjm00uF8c5UV1kzPnz5/U7F4g//0mYU8UoRjE+uJEPaWYQxHcTDv2dDofeeW0XMs1/U5Y41M7f/zAF+zvvvPM9o3aOHwnh7uLPv/CFLxzRyC6yJR+vzoV3E+4omOPRMRxUEo6uKUYxivGnZ/Dc5s/0D+oMO0BIReLCIilbyBjkw6i/k9Hv97+OM/9+x/frJ/yREO7UnqRjuKBPPPGEml1E3lxkCnsnzPl3Oj/40AyZ5AIcXwQqCnJpx+Pci1GMYvzpGN9JSOMHOTzPyz7zuE/v243JZCKlUkn+wT/4B/L000/L9zPefPNN/T4ej+WDGD8Swp1cF73XFMp5Tp2IPZ9J6pwo36szpRjFKMaP/sgLV+dvy4+8HMgL42/3vm828gieoPG7oYFeeeUVFeqMT+fXBzFIx3wQ40cmQ9UNx7lzgr+REOffCuFejGLc34P+Nud7c0mNlA382dEVLrIln4R0/H3fzeD1vxd+n9nuKysr8kGMjY0NDcn8IMaPnHAvRjGKUYxifP+jqApZjGIUoxj34SiEezGKUYxi3IejEO7FKEYxinEfjkK4F6MYxSjGfTgK4V6MYhSjGPfhKIR7MYpRjGLch6MQ7sUoRjGKcR+OQrgXoxjFKMZ9OArhXoxiFKMY9+EohHsxilGMYtyHoxDuxShGMYpxH45CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IejEO7FKEYxinEfjkK4F6MYxSjGfTgK4V6MYhSjGPfhKIR7MYpRjGLch6MQ7sUoRjGKcR+OQrgXoxjFKMZ9OArhXoxiFKMY9+EohHsxilGMYtyHoxDuxShGMYpxH45CuBejGMUoxn04CuFejGIUoxj34SiEezGKUYxi3IcjlB+h8Xf+zt+RZ599VmZnZ+XmzZvyd//u35WXXnrpO37/hQsX9Boc/+yf/TP53d/93SN/57V/4Rd+Qfb39+VXf/VXpRjFKMb9NZz8oNygDPlWg6978skn5ed+7uf0Zzdefvll+fznP/8dy55Lly7J8vKyfBBjc3NTrl+/Lh/E8ORHpBPTb/7mb8ov/uIvquDlpHKROPi7f/SP/tF3dI0bN26ogOegYvi1X/u17G9//+//ffmVX/kV/ZmLfvHiRSlGMYpx/4zPfOYz8ju/8zv682/91m/JL/3SL33D11GQ//Iv/7LKg7xQPz4cwOS1vtl49NFH5b