import Foundation

// 测试模型加载功能的脚本
class ModelLoadingTest {
    
    static func testWithSampleData() {
        print("🧪 开始测试模型加载功能...")
        
        let wrapper = FilamentWrapper()
        
        // 测试1: 空数据
        print("\n📝 测试1: 空数据")
        let emptyData = Data()
        let result1 = wrapper.loadModel(from: emptyData)
        print("结果: \(result1 ? "成功" : "失败") ✓")
        
        // 测试2: 无效数据
        print("\n📝 测试2: 无效数据")
        let invalidData = "这不是glTF文件".data(using: .utf8)!
        let result2 = wrapper.loadModel(from: invalidData)
        print("结果: \(result2 ? "成功" : "失败") ✓")
        
        // 测试3: 模拟GLB头部但数据不完整
        print("\n📝 测试3: 不完整的GLB数据")
        var glbHeader = Data()
        glbHeader.append("glTF".data(using: .ascii)!) // 魔数
        glbHeader.append(Data([0x02, 0x00, 0x00, 0x00])) // 版本2
        glbHeader.append(Data([0x64, 0x00, 0x00, 0x00])) // 长度100
        let result3 = wrapper.loadModel(from: glbHeader)
        print("结果: \(result3 ? "成功" : "失败") ✓")
        
        // 测试4: 创建默认球体
        print("\n📝 测试4: 创建默认球体")
        let result4 = wrapper.createDefaultSphere()
        print("结果: \(result4 ? "成功" : "失败") ✓")
        
        print("\n🎉 测试完成！")
    }
    
    static func createMinimalGLTF() -> Data {
        // 创建一个最小的有效glTF JSON
        let minimalGLTF = """
        {
            "asset": {
                "version": "2.0"
            },
            "scenes": [
                {
                    "nodes": [0]
                }
            ],
            "nodes": [
                {
                    "mesh": 0
                }
            ],
            "meshes": [
                {
                    "primitives": [
                        {
                            "attributes": {
                                "POSITION": 0
                            },
                            "indices": 1
                        }
                    ]
                }
            ],
            "buffers": [
                {
                    "byteLength": 48
                }
            ],
            "bufferViews": [
                {
                    "buffer": 0,
                    "byteOffset": 0,
                    "byteLength": 36
                },
                {
                    "buffer": 0,
                    "byteOffset": 36,
                    "byteLength": 12
                }
            ],
            "accessors": [
                {
                    "bufferView": 0,
                    "byteOffset": 0,
                    "componentType": 5126,
                    "count": 3,
                    "type": "VEC3",
                    "max": [1.0, 1.0, 0.0],
                    "min": [-1.0, -1.0, 0.0]
                },
                {
                    "bufferView": 1,
                    "byteOffset": 0,
                    "componentType": 5123,
                    "count": 3,
                    "type": "SCALAR"
                }
            ]
        }
        """
        
        return minimalGLTF.data(using: .utf8)!
    }
    
    static func testWithMinimalGLTF() {
        print("\n🧪 测试最小glTF文件...")
        
        let wrapper = FilamentWrapper()
        let minimalData = createMinimalGLTF()
        
        print("📊 最小glTF大小: \(minimalData.count) bytes")
        
        let result = wrapper.loadModel(from: minimalData)
        print("结果: \(result ? "成功" : "失败")")
        
        if !result {
            wrapper.diagnoseModelLoadingIssue(minimalData)
        }
    }
}

// 运行测试
// ModelLoadingTest.testWithSampleData()
// ModelLoadingTest.testWithMinimalGLTF()
