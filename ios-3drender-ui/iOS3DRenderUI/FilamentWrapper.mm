//
//  FilamentWrapper.mm
//  iOS3DRenderUI
//
//  Filament C++ 包装器实现 - 按功能模块化组织
//

#import "FilamentWrapper.h"

// 包含 C++ 标准库头文件
#include <vector>

#import <CoreGraphics/CoreGraphics.h>
#import <UIKit/UIKit.h>

// 包含 Filament C++ 头文件
#include <filament/Engine.h>
#include <filament/Renderer.h>
#include <filament/Scene.h>
#include <filament/View.h>
#include <filament/Camera.h>
#include <filament/SwapChain.h>
#include <filament/LightManager.h>
#include <filament/TransformManager.h>
#include <filament/RenderableManager.h>
#include <filament/Material.h>
#include <filament/MaterialInstance.h>
//#include <filament/filamat/MaterialBuilder.h>
#include <filament/Texture.h>
#include <filament/Viewport.h>
#include <filament/VertexBuffer.h>
#include <filament/IndexBuffer.h>
#include <filament/Skybox.h>
#include <filament/IndirectLight.h>
#include <filament/Color.h>
#include <filament/TextureSampler.h>
#include <backend/PixelBufferDescriptor.h>
#include <math/vec3.h>
#include <math/vec2.h>
#include <gltfio/AssetLoader.h>
#include <gltfio/ResourceLoader.h>
#include <gltfio/FilamentAsset.h>
#include <utils/EntityManager.h>
#include <utils/NameComponentManager.h>
#include <gltfio/materials/uberarchive.h>
#include <camutils/Manipulator.h>
#include <gltfio/TextureProvider.h>

using namespace filament;
using namespace utils;
using namespace gltfio;
using namespace camutils;

// MARK: - 常量定义
const double kNearPlane = 0.05;   // 5 cm
const double kFarPlane = 1000.0;  // 1 km
const float kScaleMultiplier = 100.0f;
const float kAperture = 16.0f;
const float kShutterSpeed = 1.0f / 125.0f;
const float kSensitivity = 100.0f;


@interface FilamentWrapper () {
    Engine* _engine;
    Renderer* _renderer;
    Scene* _scene;
    View* _view;
    Camera* _camera;
    SwapChain* _swapChain;
    CAMetalLayer *_layer;
    
    // 实体
    Entity _cameraEntity;
    Entity _lightEntity;
    Entity _modelEntity;
    
    // 模型相关
    FilamentAsset* _currentAsset;
    MaterialProvider* _materialProvider;
    AssetLoader* _assetLoader;
    ResourceLoader* _resourceLoader;

    Manipulator<float>* _manipulator;
    TextureProvider* _stbDecoder;
    TextureProvider* _ktxDecoder;
    
    // 内置几何体
    VertexBuffer* _sphereVertexBuffer;
    IndexBuffer* _sphereIndexBuffer;
    MaterialInstance* _sphereMaterial;
    
    // 球体顶点数据（用于更新颜色）
    struct SphereVertex {
        math::float3 position;
        math::float4 tangents;
        math::float2 uv;
        math::float4 color;
    };
    std::vector<SphereVertex> _sphereVertices;
    int _sphereVertexCount;
    
    // 纹理相关
    std::vector<Texture*> _textures;
    MaterialInstance* _currentMaterial;
    int _activeTextureIndex;
    math::float2 _textureOffset;
    
    // 动画
    NSTimer* _animationTimer;
    NSLock *_ctxLock;
}
@end

@implementation FilamentWrapper

// ==========================================
// MARK: - 🏗️ 初始化和生命周期管理
// ==========================================

- (instancetype)init {
    self = [super init];
    if (self) {
        _ctxLock = [NSLock new];
        
        [self initEngine];
    }
    return self;
}

- (BOOL)initEngine {
    
//    [_ctxLock lock];
//     __weak wself = self;
    BOOL (^defer)(BOOL) = ^(BOOL rtv) {
//        [wself._ctxLock unlock];
        return rtv;
    };
    
    
    // 初始化变量
    _engine = nullptr;
    _renderer = nullptr;
    _scene = nullptr;
    _view = nullptr;
    _camera = nullptr;
    _swapChain = nullptr;
    _currentAsset = nullptr;
    _materialProvider = nullptr;
    _currentMaterial = nullptr;
    
    // 初始化内置几何体变量
    _sphereVertexBuffer = nullptr;
    _sphereIndexBuffer = nullptr;
    _sphereMaterial = nullptr;
    _activeTextureIndex = 0;
    _textureOffset = {0.0f, 0.0f};
    _animationTimer = nil;
    
    // 创建 Filament 引擎
    _engine = Engine::create(Engine::Backend::METAL);
    if (!_engine) {
        NSLog(@"Failed to create Filament engine");
        return defer(NO);
    }
    
    [self _initRender];
    [self _initMaterial];
    [self _initTexture];
    [self _initView];
    
    // 设置基本光照
    [self setupLighting];
    
    // 设置相机位置
    [self setupCamera];

    return defer(YES);
}

// MARK: - 子系统初始化

- (void)_initView {
    _manipulator =
            Manipulator<float>::Builder().orbitHomePosition(0.0f, 0.0f, 4.0f).build(Mode::ORBIT);

    // 创建场景
    _scene = _engine->createScene();

    // 创建相机
    _cameraEntity = _engine->getEntityManager().create();
    _camera = _engine->createCamera(_cameraEntity);

    // 创建视图
    _view = _engine->createView();
    _view->setScene(_scene);
    _view->setCamera(_camera);
}

- (void)_initRender {
    // 创建渲染器
    _renderer = _engine->createRenderer();
}

- (void)_initTexture {
    NSLog(@"");
    _resourceLoader = new ResourceLoader({.engine = _engine, .normalizeSkinningWeights = true});
    
    // Setup texture decoder
    _stbDecoder = createStbProvider(_engine);
    _ktxDecoder = createKtx2Provider(_engine);
    _resourceLoader->addTextureProvider("image/png", _stbDecoder);
    _resourceLoader->addTextureProvider("image/jpeg", _stbDecoder);
    _resourceLoader->addTextureProvider("image/ktx2", _ktxDecoder);

}

// 材质 和 资源加载器需要同步更新
- (void)_initMaterial {
    _materialProvider = createUbershaderProvider(_engine, UBERARCHIVE_DEFAULT_DATA, UBERARCHIVE_DEFAULT_SIZE);
    EntityManager& em = EntityManager::get();
    NameComponentManager* ncm = new NameComponentManager(em);
    _assetLoader = AssetLoader::create({_engine, _materialProvider, ncm, &em});
    MaterialProvider* mt = &_assetLoader->getMaterialProvider();
    assert(mt == _materialProvider);
}

// MARK: - 资源清理

- (void)dealloc {
    [self cleanup];
}

- (void)cleanup {
    if (_animationTimer) {
        [_animationTimer invalidate];
        _animationTimer = nil;
    }
    
    if (_engine) {
        [self removeCurrentModel];
        [self clearTextures];
        
        if (_currentMaterial) {
            _engine->destroy(_currentMaterial);
            _currentMaterial = nullptr;
        }
        
        // 清理材质提供器
        if (_materialProvider) {
//            MaterialProvider::destroy(&_materialProvider);
            _materialProvider = nullptr;
        }
        
        if (_swapChain) {
            _engine->destroy(_swapChain);
            _swapChain = nullptr;
        }
        
        // 清理内置几何体
        if (_sphereVertexBuffer) {
            _engine->destroy(_sphereVertexBuffer);
            _sphereVertexBuffer = nullptr;
        }
        if (_sphereIndexBuffer) {
            _engine->destroy(_sphereIndexBuffer);
            _sphereIndexBuffer = nullptr;
        }
        if (_sphereMaterial) {
            _engine->destroy(_sphereMaterial);
            _sphereMaterial = nullptr;
        }
        
        _engine->destroyCameraComponent(_cameraEntity);
        _engine->destroy(_view);
        _engine->destroy(_scene);
        _engine->destroy(_renderer);
        
        Engine::destroy(_engine);
        _engine = nullptr;
    }
}

// ==========================================
// MARK: - 🌅 场景和光照管理
// ==========================================

- (void)setupLighting {
    // 创建点光源，位于摄像机斜后上方60度
    _lightEntity = _engine->getEntityManager().create();
    
    // 计算光源位置：摄像机在 (0, 0, 8)，球体在 (0, 0, 0)
    // 斜后上方60度角度计算
    float cameraDistance = 8.0f;
    float lightDistance = cameraDistance * 0.8f; // 距离适中
    
    // 60度角：30度向上，30度向后偏移
    float angleRad = 60.0f * M_PI / 180.0f;
    float lightX = lightDistance * sin(angleRad) * 0.6f; // 向右偏移
    float lightY = lightDistance * sin(angleRad) * 0.8f; // 向上偏移  
    float lightZ = cameraDistance + lightDistance * cos(angleRad) * 0.5f; // 向后偏移
    
    math::float3 lightPosition = {lightX, lightY, lightZ};
    
    LightManager::Builder(LightManager::Type::SUN)
//        .color(Color::toLinear<ACCURATE>({1.0f, 1.0f, 1.0f}))
        .color(Color::cct(6500.0f))
        .intensity(80000.0f) // 点光源适中强度
        .position(lightPosition)
        .falloff(15.0f) // 光衰减半径
        .castShadows(false)
        .build(*_engine, _lightEntity);
    
    // 添加到场景
    _scene->addEntity(_lightEntity);
    
    NSLog(@"✨ 设置点光源完成，位置: (%.2f, %.2f, %.2f)", lightX, lightY, lightZ);
    
    // 添加柔和的环境光以确保球体整体可见
    auto skybox = Skybox::Builder()
        .color({0.1f, 0.1f, 0.15f, 1.0f}) // 较暗的环境色，突出点光源
        .build(*_engine);
    _scene->setSkybox(skybox);
    
    // 设置间接光照
    auto ibl = IndirectLight::Builder()
        .intensity(30000.0f) // 降低环境光，突出点光源效果
        .build(*_engine);
    _scene->setIndirectLight(ibl);
    
    NSLog(@"🌅 环境光照设置完成，强度已调低突出点光源效果");
}

- (void)setupCamera {
    // 设置相机变换
    auto& tcm = _engine->getTransformManager();
    // 优化相机位置：球体半径2.0，距离8.0，使球体占视野约50%
    const double distance = 6.0;
    math::float3 eye = {0, 0, distance};    // 适当距离，确保球体完整显示
    math::float3 center = {0, 0, 0}; // 看向原点（球体中心）
    math::float3 up = {0, 1, 0};     // Y轴向上
    
    math::float3 zAxis = normalize(eye - center);
    math::float3 xAxis = normalize(cross(up, zAxis));
    math::float3 yAxis = cross(zAxis, xAxis);
    
    math::mat4f transform = {
        math::float4(xAxis, 0),
        math::float4(yAxis, 0),
        math::float4(zAxis, 0),
        math::float4(eye, 1)
    };
    
    tcm.setTransform(tcm.getInstance(_cameraEntity), transform);
    
    // 设置投影矩阵
    const double aspect = 1.0; // 将在 resize 时更新
    const double fovInDegrees = 0.0; //45.0;
    const double near = 0.1;
    const double far = 100.0;
    
    _camera->setProjection(fovInDegrees, aspect, near, far);
//    _camera->setExposure(kAperture, kShutterSpeed, kSensitivity);
    
    // 计算视野信息用于调试
    double halfFovRad = (fovInDegrees * M_PI / 180.0) / 2.0;
    double visibleRadius = tan(halfFovRad) * distance; // 距离8.0时的可见半径
    NSLog(@"相机设置: 距离=8.0, 视野=%.1f°, 可见半径=%.2f, 球体半径=1.5", fovInDegrees, visibleRadius);
}

// ==========================================
// MARK: - 🖼️ 表面和视图管理
// ==========================================

- (BOOL)setupWithMetalLayer:(CAMetalLayer *)metalLayer {
    if (!_engine || !metalLayer) {
        return NO;
    }
    
    // 创建交换链
    _layer = metalLayer;
    _swapChain = _engine->createSwapChain((__bridge void*)metalLayer);
    return _swapChain != nullptr;
}

- (void)resizeWithWidth:(NSUInteger)width height:(NSUInteger)height {
    if (_view) {
        _manipulator->setViewport(_layer.bounds.size.width, _layer.bounds.size.height);
        _view->setViewport({0, 0, (uint32_t)width, (uint32_t)height});
    }
    
    if (_camera && width > 0 && height > 0) {
        const double aspect = (double)width / (double)height;
        const double fovInDegrees = 45.0;
        const double near = 0.05; // 0.1;
        const double far = 1000; //100.0;
        
        _camera->setProjection(fovInDegrees, aspect, near, far);
    }
}

// ==========================================
// MARK: - 🎨 渲染系统
// ==========================================

- (void)render {
    if (!_renderer || !_swapChain || !_view) {
        NSLog(@"渲染器、交换链或视图为空");
        return;
    }
    
    static int frameCount = 0;
    frameCount++;
    
    if (_renderer->beginFrame(_swapChain)) {
        _renderer->render(_view);
        _renderer->endFrame();
        
        // 每100帧打印一次调试信息
        if (frameCount % 100 == 0) {
            NSLog(@"已渲染 %d 帧，场景中实体数量: %zu", frameCount, _scene->getEntityCount());
        }
    }
}

// ==========================================
// MARK: - 📦 模型加载和管理
// ==========================================

// 验证模型数据格式
- (BOOL)validateModelData:(NSData *)data {
    if (!data || data.length < 4) {
        NSLog(@"❌ 数据为空或太小");
        return NO;
    }

    const uint8_t* bytes = (const uint8_t*)data.bytes;

    // 检查 GLB 格式 (二进制 glTF)
    if (data.length >= 12 &&
        bytes[0] == 'g' && bytes[1] == 'l' &&
        bytes[2] == 'T' && bytes[3] == 'F') {

        // 检查版本号 (应该是 2)
        uint32_t version = *(uint32_t*)(bytes + 4);
        if (version != 2) {
            NSLog(@"⚠️ GLB 版本 %u 可能不被支持，推荐版本 2", version);
        }

        // 检查文件长度
        uint32_t length = *(uint32_t*)(bytes + 8);
        if (length != data.length) {
            NSLog(@"⚠️ GLB 文件长度不匹配：头部声明 %u，实际 %zu", length, data.length);
        }

        NSLog(@"✅ 有效的 GLB 文件，版本: %u，长度: %u", version, length);
        return YES;
    }

    // 检查 JSON glTF 格式
    NSString* dataString = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    if (dataString && [dataString containsString:@"\"asset\""]) {
        NSLog(@"✅ 检测到 JSON glTF 格式");
        return YES;
    }

    NSLog(@"❌ 无法识别的文件格式，不是有效的 glTF/GLB 文件");
    return NO;
}

- (BOOL)loadModelFromData:(NSData *)data {
    if (!_engine || !data) {
        NSLog(@"❌ 模型加载失败：引擎或数据为空");
        return NO;
    }
    // 移除旧模型
    [self removeCurrentModel];

    NSLog(@"🔄 开始加载模型，数据大小: %zu bytes", data.length);
    // 加载材质
    if (!_currentMaterial) {
        _currentMaterial = [self createSimpleMaterial];
        if (!_currentMaterial) {
            NSLog(@"材质加载失败.");
            return NO;
        }
    }
    // 验证数据格式
    if (![self validateModelData:data]) {
        return NO;
    }
    
    // 加载模型（改进错误处理）
    try {
        // 确保数据指针有效
        const uint8_t* dataBytes = (const uint8_t*)data.bytes;
        uint32_t dataLength = (uint32_t)data.length;
        
        NSLog(@"🔄 调用 createAsset，数据指针: %p，长度: %u", dataBytes, dataLength);
        
        // 核心调用
        _currentAsset = _assetLoader->createAsset(dataBytes, dataLength);

        if (!_currentAsset) {
            NSLog(@"❌ createAsset 返回 nullptr");
            NSLog(@"   可能原因：");
            NSLog(@"   1. 文件格式无效或损坏");
            NSLog(@"   2. glTF 版本不支持（需要 2.0）");
            NSLog(@"   3. 文件内容为空或截断");
            NSLog(@"   4. 材质提供器配置问题");
            // 执行详细诊断
            [self diagnoseModelLoadingIssue:data];
            return NO;
        }
        NSLog(@"✅ 模型资产创建成功");

    } catch (const std::exception& e) {
        NSLog(@"❌ 加载模型时发生异常: %s", e.what());
        return NO;
    } catch (...) {
        NSLog(@"❌ 加载模型时发生未知异常");
        return NO;
    }

    if (!_currentAsset) {
        NSLog(@"❌ 模型资产为空");
        return NO;
    }
    
    // 检查资产内容
    size_t entityCount = _currentAsset->getRenderableEntityCount();
    auto const& renderableEntities = _currentAsset->getRenderableEntities();
    NSLog(@"📊 模型包含 %zu 个实体", entityCount);

    if (entityCount == 0) {
        NSLog(@"⚠️ 模型不包含任何实体，可能是空文件或格式问题");
        return NO;
    }

    // 添加实体对象到场景中
    NSLog(@"entity count: %zu", _scene->getEntityCount());
    _scene->addEntities(renderableEntities, entityCount);
    _modelEntity = _currentAsset->getRoot();
    NSLog(@"entity count: %zu", _scene->getEntityCount());
    if (!_resourceLoader) {
        NSLog(@"❌ ResourceLoader 未创建");
        return NO;
    }


    try {
        NSLog(@"🔄 开始加载模型资源...");
        // 加载资源（纹理、材质等）
        if (!_resourceLoader->loadResources(_currentAsset)) {
            NSLog(@"❌ 资源加载失败，可能的原因：");
            NSLog(@"   1. 纹理文件缺失或格式不支持");
            NSLog(@"   2. 材质定义有问题");
            NSLog(@"   3. 内存不足");
            return NO;
        }

        NSLog(@"✅ 模型资源加载成功");
    } catch (const std::exception& e) {
        NSLog(@"❌ 加载资源时发生异常: %s", e.what());
        return NO;
    } catch (...) {
        NSLog(@"❌ 加载资源时发生未知异常");
        return NO;
    }

    // 添加到场景
//#if DEBUG
//    const Entity* entities = _currentAsset->getEntities();
//    entityCount = _currentAsset->getEntityCount(); // 重新获取，确保一致性
//
//    NSLog(@"🎭 将 %zu 个实体添加到场景", entityCount);
//
//    for (size_t i = 0; i < entityCount; ++i) {
//        NSLog(@"   实体 %zu: ID = %d", i, entities[i].getId());
//    }
//
//    if (entityCount > 0) {
//        _modelEntity = entities[0];
//        NSLog(@"🎯 主模型实体设置为: %d", _modelEntity.getId());
//    }
//
//    NSLog(@"🎉 模型加载完成！场景中实体总数: %zu", _scene->getEntityCount());
//#endif
    
    // 构建可渲染组件
    auto const& entities = _currentAsset->getRenderableEntities();
    auto const& renderableCount = _currentAsset->getRenderableEntityCount();

//    RenderableManager::Builder(1)
//      .boundingBox({{0,0,0},{1,1,1}})
//      .geometry(0, RenderableManager::PrimitiveType::TRIANGLES, vbo, ibo)
//      .material(0, _currentMaterial)                 // 把材质实例挂到 primitive 上
//      .build(*_engine, entities);
    
//    auto& rcm = _engine->getRenderableManager();
//    for (int i=0; i< renderableCount; i++) {
//        auto e = entities[i];
//        auto ih = rcm.getInstance(e);
//        size_t primCount = rcm.getPrimitiveCount(ih);
//        for (size_t i = 0; i < primCount; ++i) {
//            //MaterialInstance* mi = rcm.getMaterialInstanceAt(ih, i);
//
//            // 直接修改参数（例如调 roughness）
//            //mi->setParameter("roughness", 0.6f);
//            //mi->setParameter("baseColorMap", baseColorTex, sampler);
//            //rcm.setReceiveShadows(ih, true);
//            // 替换材质实例：
//            //MaterialInstance const* _mat = rcm.getMaterialInstanceAt(ih, i);
//            rcm.setMaterialInstanceAt(ih, i, _currentMaterial);
//        }
//    }
    
    [self transformToUnitCube];
    //[self centerModelAtOrigin];
//    [self standardizeModel];

    return YES;
}

// ==========================================
// MARK: - 🔧 模型变换和几何处理
// ==========================================

// 完整的模型标准化方法
- (void)standardizeModel {
    if (!_currentAsset) return;
    
    // 步骤1：计算边界框
    auto aabb = _currentAsset->getBoundingBox();
    math::float3 center = (aabb.min + aabb.max) * 0.5f;
    math::float3 size = aabb.max - aabb.min;
    float maxDimension = fmax(fmax(size.x, size.y), size.z);
    
    // 步骤2：定义目标大小（例如：所有模型最大尺寸为2.0单位）
    float targetSize = 2.0f;
    float scaleFactor = targetSize / maxDimension;
    
    // 步骤3：创建标准化变换矩阵
    auto& tm = _engine->getTransformManager();
    math::mat4f transform = 
        math::mat4f::scaling(scaleFactor) *           // 先缩放
        math::mat4f::translation(-center);           // 再居中
    
    // 步骤4：应用变换
    tm.setTransform(tm.getInstance(_currentAsset->getRoot()), transform);
    
    NSLog(@"📊 模型标准化完成: 原始大小[%.2f,%.2f,%.2f] -> 缩放因子%.3f", 
          size.x, size.y, size.z, scaleFactor);
}

- (void)transformToUnitCube {
    if (!_currentAsset) {
        return;
    }
    auto& tm = _engine->getTransformManager();
    auto aabb = _currentAsset->getBoundingBox();
    auto center = (aabb.min + aabb.max) * 0.5f;
    auto halfExtent = (aabb.max - aabb.min) * 0.5f;
    auto maxExtent = max(halfExtent) * 2;
    auto scaleFactor = 2.0f / maxExtent;
    auto transform = math::mat4f::scaling(scaleFactor) * math::mat4f::translation(-center);
    tm.setTransform(tm.getInstance(_currentAsset->getRoot()), transform);
}

// 将模型中心移动到原点
- (void)centerModelAtOrigin {
    if (!_currentAsset || !_engine) {
        NSLog(@"❌ 无法居中模型：资产或引擎为空");
        return;
    }
    
    NSLog(@"🔄 开始检测模型坐标并移动到原点...");
    
    // 获取变换管理器
    auto& tm = _engine->getTransformManager();
    
    // 计算模型的边界框
    Aabb modelBounds = [self calculateModelBounds];
    
    // 获取边界框的中心点
    math::float3 center = (modelBounds.min + modelBounds.max) * 0.5f;
    NSLog(@"📊 模型当前中心点: (%.3f, %.3f, %.3f)", center.x, center.y, center.z);
    
    // 计算需要的偏移量（将中心移动到原点）
    math::float3 offset = -center;
    NSLog(@"📊 计算偏移量: (%.3f, %.3f, %.3f)", offset.x, offset.y, offset.z);
    
    // 创建平移变换矩阵
    math::mat4f translationMatrix = math::mat4f::translation(offset);
    
    // 获取模型根实体的当前变换
    Entity rootEntity = _currentAsset->getRoot();
    auto instance = tm.getInstance(rootEntity);
    
    if (instance) {
        // 获取当前变换矩阵
        math::mat4f currentTransform = tm.getTransform(instance);
        
        // 应用平移变换（在当前变换基础上）
        math::mat4f newTransform = translationMatrix * currentTransform;
        
        // 设置新的变换矩阵
        tm.setTransform(instance, newTransform);
        
        NSLog(@"✅ 模型已成功移动到原点");
        
        // 验证结果
        [self verifyModelPosition];
    } else {
        NSLog(@"❌ 无法获取模型根实体的变换实例");
    }
}

// 计算模型的边界框
- (Aabb)calculateModelBounds {
    if (!_currentAsset || !_engine) {
        return Aabb();
    }
    
    auto& rm = _engine->getRenderableManager();
    auto& tm = _engine->getTransformManager();
    
    // 初始化边界框
    Aabb totalBounds;
    bool firstEntity = true;
    
    // 遍历所有实体
    const Entity* entities = _currentAsset->getEntities();
    size_t entityCount = _currentAsset->getEntityCount();
    
    for (size_t i = 0; i < entityCount; ++i) {
        Entity entity = entities[i];
        // 检查实体是否有可渲染组件
        if (rm.hasComponent(entity)) {
            auto renderableInstance = rm.getInstance(entity);
            
            // 获取实体的局部边界框
            Box localBounds = rm.getAxisAlignedBoundingBox(renderableInstance);
            
            // 获取实体的世界变换矩阵
            math::mat4f worldTransform = math::mat4f(); // 单位矩阵
            if (tm.hasComponent(entity)) {
                auto transformInstance = tm.getInstance(entity);
                worldTransform = tm.getWorldTransform(transformInstance);
            }
            
            // 将局部边界框变换到世界坐标
            // Box 类型没有 transform 方法，需要手动计算
            math::float3 localCenter = localBounds.center;
            math::float3 localHalfExtent = localBounds.halfExtent;
            
            // 变换中心点
            math::float4 worldCenter4 = worldTransform * math::float4(localCenter, 1.0f);
            math::float3 worldCenter = worldCenter4.xyz;
            
            // 对于边界框，我们需要考虑变换对尺寸的影响
            // 简化处理：假设只有平移和均匀缩放
            math::float3 worldHalfExtent = localHalfExtent;
            
            // 创建世界坐标的边界框
            Aabb worldBounds;
            worldBounds.min = worldCenter - worldHalfExtent;
            worldBounds.max = worldCenter + worldHalfExtent;
            
            // 合并到总边界框
            if (firstEntity) {
                totalBounds = worldBounds;
                firstEntity = false;
            } else {
                totalBounds.min = min(totalBounds.min, worldBounds.min);
                totalBounds.max = max(totalBounds.max, worldBounds.max);
            }
            
            // 计算实体的xyz三维大小（完整尺寸 = 半范围 * 2）
            math::float3 entitySize = localHalfExtent * 2.0f;
            
            NSLog(@"   实体 %zu: 局部边界中心 [%.3f,%.3f,%.3f] 半范围 [%.3f,%.3f,%.3f]", 
                  i, 
                  localCenter.x, localCenter.y, localCenter.z,
                  localHalfExtent.x, localHalfExtent.y, localHalfExtent.z);
            NSLog(@"   实体 %zu: XYZ三维大小 [%.3f,%.3f,%.3f] (宽度x高度x深度)", 
                  i, 
                  entitySize.x, entitySize.y, entitySize.z);
        }
    }
    
    NSLog(@"📊 总边界框: [%.3f,%.3f,%.3f] - [%.3f,%.3f,%.3f]", 
          totalBounds.min.x, totalBounds.min.y, totalBounds.min.z,
          totalBounds.max.x, totalBounds.max.y, totalBounds.max.z);
    
    return totalBounds;
}

// 验证模型位置
- (void)verifyModelPosition {
    if (!_currentAsset || !_engine) {
        return;
    }
    
    NSLog(@"🔍 验证模型位置...");
    
    // 重新计算边界框
    Aabb newBounds = [self calculateModelBounds];
    math::float3 newCenter = (newBounds.min + newBounds.max) * 0.5f;
    
    NSLog(@"📊 移动后的模型中心点: (%.3f, %.3f, %.3f)", newCenter.x, newCenter.y, newCenter.z);
    
    // 检查是否接近原点（允许小的浮点误差）
    float tolerance = 0.001f;
    if (abs(newCenter.x) < tolerance && abs(newCenter.y) < tolerance && abs(newCenter.z) < tolerance) {
        NSLog(@"✅ 模型已成功居中到原点");
    } else {
        NSLog(@"⚠️ 模型中心与原点仍有偏差，可能需要进一步调整");
    }
}

// 可选：提供手动设置模型位置的方法
- (void)moveModelToPosition:(math::float3)targetPosition {
    if (!_currentAsset || !_engine) {
        NSLog(@"❌ 无法移动模型：资产或引擎为空");
        return;
    }
    
    auto& tm = _engine->getTransformManager();
    
    // 计算当前模型中心
    Aabb currentBounds = [self calculateModelBounds];
    math::float3 currentCenter = (currentBounds.min + currentBounds.max) * 0.5f;
    
    // 计算偏移量
    math::float3 offset = targetPosition - currentCenter;
    
    // 应用变换
    Entity rootEntity = _currentAsset->getRoot();
    auto instance = tm.getInstance(rootEntity);
    
    if (instance) {
        math::mat4f currentTransform = tm.getTransform(instance);
        math::mat4f translationMatrix = math::mat4f::translation(offset);
        math::mat4f newTransform = translationMatrix * currentTransform;
        
        tm.setTransform(instance, newTransform);
        
        NSLog(@"✅ 模型已移动到位置: (%.3f, %.3f, %.3f)", 
              targetPosition.x, targetPosition.y, targetPosition.z);
    }
}

// 诊断模型加载问题
- (void)diagnoseModelLoadingIssue:(NSData *)data {
    NSLog(@"🔍 开始诊断模型加载问题...");

    // 基本信息
    NSLog(@"📊 数据大小: %zu bytes", data.length);
    NSLog(@"📊 引擎状态: %s", _engine ? "正常" : "无效");

    if (!data || data.length == 0) {
        NSLog(@"❌ 数据为空");
        return;
    }

    // 内存检查
    NSProcessInfo* processInfo = [NSProcessInfo processInfo];
    NSLog(@"📊 物理内存: %.2f GB", processInfo.physicalMemory / (1024.0 * 1024.0 * 1024.0));

    // 文件格式分析
    const uint8_t* bytes = (const uint8_t*)data.bytes;
    NSLog(@"📊 文件头 (前16字节): ");
    for (int i = 0; i < MIN(16, data.length); i++) {
        printf("%02X ", bytes[i]);
    }
    printf("\n");

    // 尝试解析为字符串（如果是 JSON）
    if (data.length > 0 && bytes[0] == '{') {
        NSString* jsonString = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
        if (jsonString) {
            NSLog(@"📄 JSON 内容预览: %@", [jsonString substringToIndex:MIN(200, jsonString.length)]);
        }
    }

    // 常见问题检查
    NSLog(@"🔍 常见问题检查:");
    NSLog(@"   - 文件是否为空: %s", data.length == 0 ? "是" : "否");
    NSLog(@"   - 文件是否太小: %s", data.length < 100 ? "是" : "否");
    NSLog(@"   - 是否为 GLB 格式: %s", (data.length >= 4 && bytes[0] == 'g' && bytes[1] == 'l' && bytes[2] == 'T' && bytes[3] == 'F') ? "是" : "否");
    NSLog(@"   - 是否可能为 JSON: %s", (data.length > 0 && bytes[0] == '{') ? "是" : "否");

    NSLog(@"💡 建议:");
    NSLog(@"   1. 确保文件是有效的 glTF 2.0 或 GLB 格式");
    NSLog(@"   2. 检查文件是否完整下载");
    NSLog(@"   3. 尝试使用在线 glTF 验证器验证文件");
    NSLog(@"   4. 确保模型不包含不支持的扩展");
}

- (void)removeCurrentModel {
    if (_engine) {
        
        if (!_modelEntity.isNull()) {
            _scene->remove(_modelEntity);
            _modelEntity = {};
        }

        if (_currentAsset) {
            // 驱逐资源
            _resourceLoader->evictResourceData();
            // 场景中释放模型
            const Entity* entities = _currentAsset->getRenderableEntities();
            size_t entityCount = _currentAsset->getRenderableEntityCount();
            _scene->removeEntities(entities, entityCount);
            
            // 需要保留 AssetLoader 实例来销毁资产
            _assetLoader->destroyAsset(_currentAsset);
            _currentAsset = nullptr;
            NSLog(@"Clean _currentAsset");
        }
        
        if (_sphereMaterial) {
            _engine->destroy(_sphereMaterial);
            _sphereMaterial = nullptr;
        }
        if (_currentMaterial) {
            _engine->destroy(_currentMaterial);
            _currentMaterial = nullptr;
        }
    }
    
}

// 创建一个简单的材质，支持基本颜色和纹理
- (MaterialInstance*)createSimpleMaterial {
    // 方案1: 尝试加载预编译的自定义材质
    MaterialInstance* instance = [self loadCustomMaterial];
    if (instance) {
        NSLog(@"✅ 成功创建自定义纹理材质");
        return instance;
    }
    
    // 方案2: 创建一个运行时的简单材质（无纹理支持）
    NSLog(@"⚠️ 无法加载自定义材质，使用简化方案");
    
    // 使用默认材质，但优化参数设置
    try {
        instance = _engine->getDefaultMaterial()->createInstance();
        
        const Material* mat = instance->getMaterial();
        NSLog(@"使用默认材质: %s", mat->getName());
        
        // 检查并设置支持的参数
        std::vector<std::string> supportedParams;
        
        // 尝试设置基础颜色（更亮一些）
        math::float3 brightColor = {0.9f, 0.9f, 0.9f};
        
        if (mat->hasParameter("baseColor")) {
            instance->setParameter("baseColor", RgbType::sRGB, brightColor);
            supportedParams.push_back("baseColor");
        }
        
        if (mat->hasParameter("color")) {
            instance->setParameter("color", RgbType::sRGB, brightColor);
            supportedParams.push_back("color");
        }
        
        if (mat->hasParameter("baseColorFactor")) {
            instance->setParameter("baseColorFactor", 1.0f);
            supportedParams.push_back("baseColorFactor");
        }
        
        // 设置 PBR 参数使材质更加明亮
        if (mat->hasParameter("metallicFactor")) {
            instance->setParameter("metallicFactor", 0.0f); // 非金属
            supportedParams.push_back("metallicFactor");
        }
        
        if (mat->hasParameter("roughnessFactor")) {
            instance->setParameter("roughnessFactor", 0.5f); // 中等粗糙度
            supportedParams.push_back("roughnessFactor");
        }
        
        NSLog(@"默认材质支持的参数数量: %zu", supportedParams.size());
        for (const std::string& param : supportedParams) {
            NSLog(@"  - %s", param.c_str());
        }
        
        if (supportedParams.empty()) {
            NSLog(@"⚠️ 默认材质不支持任何颜色参数");
        }
        
    } catch (const std::exception& e) {
        NSLog(@"❌ 创建材质时发生异常: %s", e.what());
        instance = _engine->getDefaultMaterial()->createInstance();
    } catch (...) {
        NSLog(@"❌ 创建材质时发生未知异常");
        instance = _engine->getDefaultMaterial()->createInstance();
    }
    
    return instance;
}

// 尝试创建运行时的简单材质（替代预编译材质）
- (MaterialInstance*)loadCustomMaterial {
    NSLog(@"🔧 尝试创建支持颜色的材质...");
    
    // 策略1: 尝试创建一个基于源码的最简单 unlit 材质
    MaterialInstance* instance = [self createRuntimeUnlitMaterial];
    if (instance) {
        NSLog(@"✅ 成功创建运行时 unlit 材质");
        return instance;
    }
    
    // 策略2: 检查是否有其他可用的内置材质
    NSLog(@"🔄 尝试查找其他内置材质...");
    
    // 尝试默认材质，但深度检查所有参数
    try {
        instance = _engine->getDefaultMaterial()->createInstance();
        const Material* mat = instance->getMaterial();
        
        NSLog(@"检查材质: %s", mat->getName());
        
        // 完整检查所有可能的参数名
        NSArray* allPossibleParams = @[
            // 颜色参数
            @"baseColor", @"color", @"diffuseColor", @"albedo", @"tint", @"mainColor",
            // 因子参数  
            @"baseColorFactor", @"colorFactor", @"diffuseFactor", @"albedoFactor",
            // PBR 参数
            @"metallicFactor", @"roughnessFactor", @"emissiveFactor",
            // 纹理参数
            @"baseColorMap", @"diffuseMap", @"colorMap", @"albedoMap", @"mainTexture"
        ];
        
        NSMutableArray* supportedParams = [NSMutableArray array];
        
        for (NSString* paramName in allPossibleParams) {
            const char* cParamName = [paramName UTF8String];
            if (mat->hasParameter(cParamName)) {
                [supportedParams addObject:paramName];
            }
        }
        
        NSLog(@"🔍 检查完成，支持的参数: %@", supportedParams);
        
        if (supportedParams.count > 0) {
            NSLog(@"✅ 找到支持参数的材质！");
            return instance;
        } else {
            NSLog(@"❌ 默认材质不支持任何参数，需要使用顶点颜色方案");
            return instance; // 返回但标记为需要特殊处理
        }
        
    } catch (const std::exception& e) {
        NSLog(@"❌ 检查默认材质时发生异常: %s", e.what());
        return nullptr;
    } catch (...) {
        NSLog(@"❌ 检查默认材质时发生未知异常");
        return nullptr;
    }
}

// 创建最简单的运行时 unlit 材质
- (MaterialInstance*)createRuntimeUnlitMaterial {
    NSLog(@"🛠️ 尝试创建预构建 unlit 材质...");
    
    try {
        // Filament 暂不支持运行时编译 (filamat 编译时未开启，会增加包体积)
        NSString *matPath = [NSBundle.mainBundle pathForResource:@"baseColor" ofType:@"filamat" inDirectory:@"material"];
        if (!matPath) {
            return nullptr;
        }
        NSData *matData = [NSData dataWithContentsOfFile:matPath];
        auto* mat = filament::Material::Builder().package(matData.bytes, matData.length).build(*_engine);
        return mat->createInstance();
    } catch (...) {
        NSLog(@"❌ 无法创建预构建材质");
        return nullptr;
    }
}

// 动态调整相机距离
- (void)setCameraDistance:(float)distance {
    if (!_engine || _cameraEntity.isNull()) return;
    
    auto& tcm = _engine->getTransformManager();
    math::float3 eye = {0, 0, distance};
    math::float3 center = {0, 0, 0};
    math::float3 up = {0, 1, 0};
    
    math::float3 zAxis = normalize(eye - center);
    math::float3 xAxis = normalize(cross(up, zAxis));
    math::float3 yAxis = cross(zAxis, xAxis);
    
    math::mat4f transform = {
        math::float4(xAxis, 0),
        math::float4(yAxis, 0),
        math::float4(zAxis, 0),
        math::float4(eye, 1)
    };
    
    tcm.setTransform(tcm.getInstance(_cameraEntity), transform);
    
    // 计算并输出视野信息
    double halfFovRad = (45.0 * M_PI / 180.0) / 2.0;
    double visibleRadius = tan(halfFovRad) * distance;
    NSLog(@"相机距离已调整为: %.1f, 可见半径: %.2f", distance, visibleRadius);
}

// 尝试应用纹理到材质
- (BOOL)tryApplyTextureToMaterial:(MaterialInstance*)material texture:(Texture*)texture {
    if (!material || !texture) return NO;
    
    const Material* mat = material->getMaterial();
    NSLog(@"Material name: %s", material->getName());
    TransparencyMode transpMode = material->getTransparencyMode();
    // 创建合适的纹理采样器
    TextureSampler sampler = TextureSampler(
        TextureSampler::MinFilter::LINEAR,
        TextureSampler::MagFilter::LINEAR,
        TextureSampler::WrapMode::REPEAT
    );
    
    // 尝试常见的纹理参数名称（按优先级排序）
    NSArray* textureParams = @[
        @"baseColorMap",      // PBR 材质的主要纹理参数
        @"diffuseMap",        // 传统材质的漫反射纹理
        @"albedoMap",         // 另一种常见的颜色纹理名称
        @"colorMap",          // 通用颜色纹理
        @"texture",           // 最基本的纹理参数
        @"mainTexture",       // Unity 风格的主纹理
        @"diffuse"            // 简化的漫反射参数
    ];
    
    NSMutableArray* availableParams = [NSMutableArray array];
    NSMutableArray* attemptedParams = [NSMutableArray array];
    
    auto const& entities = _currentAsset->getRenderableEntities();
    auto const& renderableCount = _currentAsset->getRenderableEntityCount();
    auto& rcm = _engine->getRenderableManager();
    for (int i=0; i< renderableCount; i++) {
        auto e = entities[i];
        auto ih = rcm.getInstance(e);
        size_t primCount = rcm.getPrimitiveCount(ih);
        for (size_t i = 0; i < primCount; ++i) {
            MaterialInstance* mi = rcm.getMaterialInstanceAt(ih, i);

            // 直接修改参数（例如调 roughness）
            //mi->setParameter("roughness", 0.6f);
            mi->setParameter("baseColorMap", texture, sampler);
            //rcm.setReceiveShadows(ih, true);
            // 替换材质实例：
            //MaterialInstance const* _mat = rcm.getMaterialInstanceAt(ih, i);
            rcm.setMaterialInstanceAt(ih, i, _currentMaterial);
        }
    }
    
    // 首先检查所有可用的纹理参数
    for (NSString* paramName in textureParams) {
        const char* cParamName = [paramName UTF8String];
        if (mat->hasParameter(cParamName)) {
            [availableParams addObject:paramName];
            //material->setParameter(cParamName, texture, sampler);
        }
    }
    
    NSLog(@"🔍 材质支持的纹理参数: %@", availableParams);
    
    if (availableParams.count > 0) {
        // 尝试设置每个可用的纹理参数
        for (NSString* paramName in availableParams) {
            const char* cParamName = [paramName UTF8String];
            [attemptedParams addObject:paramName];
            
            try {
                material->setParameter(cParamName, texture, sampler);
                NSLog(@"✅ 成功设置纹理参数: %@", paramName);
                return YES;
            } catch (const std::exception& e) {
                NSLog(@"❌ 设置纹理参数 %@ 时发生异常: %s", paramName, e.what());
            } catch (...) {
                NSLog(@"❌ 设置纹理参数 %@ 时发生未知异常", paramName);
            }
        }
        
        NSLog(@"⚠️ 所有纹理参数设置尝试都失败了，尝试的参数: %@", attemptedParams);
    } else {
        NSLog(@"⚠️ 材质不支持任何纹理参数，将使用颜色方案模拟纹理");
    }
    
    // 如果不能直接应用纹理，我们尝试从纹理中提取主要颜色
    //return [self simulateTextureWithColor:material textureIndex:_activeTextureIndex];
    return YES;
}

// 通过顶点颜色模拟纹理效果
- (BOOL)simulateTextureWithColor:(MaterialInstance*)material textureIndex:(int)textureIndex {
    NSLog(@"🎨 使用顶点颜色模拟纹理效果 (纹理索引: %d)", textureIndex);
    
    // 定义一些基于纹理内容的代表性颜色
    static const math::float4 textureColors[] = {
        {0.3f, 0.5f, 0.9f, 1.0f}, // 天蓝色 - 默认蓝色纹理
        {0.2f, 0.7f, 0.3f, 1.0f}, // 翠绿色 - 模拟草地纹理
        {0.8f, 0.4f, 0.2f, 1.0f}, // 温暖的棕色 - 模拟木纹纹理
        {0.9f, 0.8f, 0.3f, 1.0f}, // 金黄色 - 模拟金属纹理
        {0.6f, 0.3f, 0.8f, 1.0f}, // 紫色 - 模拟神秘纹理
        {0.9f, 0.5f, 0.6f, 1.0f}, // 粉红色 - 模拟花朵纹理
        {0.5f, 0.5f, 0.5f, 1.0f}, // 银灰色 - 模拟金属纹理
        {0.9f, 0.6f, 0.2f, 1.0f}, // 橙色 - 模拟火焰纹理
    };
    
    int colorCount = sizeof(textureColors) / sizeof(textureColors[0]);
    int colorIndex = textureIndex % colorCount;
    math::float4 simulatedColor = textureColors[colorIndex];
    
    // 更新球体顶点颜色
    return [self updateSphereVertexColor:simulatedColor];
}

// 更新球体顶点颜色
- (BOOL)updateSphereVertexColor:(math::float4)color {
    if (!_sphereVertexBuffer || _sphereVertices.empty()) {
        NSLog(@"❌ 球体顶点缓冲区或数据不存在");
        return NO;
    }
    
    NSLog(@"🌈 更新球体顶点颜色: (%.2f, %.2f, %.2f, %.2f)", color.r, color.g, color.b, color.a);
    
    // 更新所有顶点的颜色
    for (size_t i = 0; i < _sphereVertices.size(); i++) {
        _sphereVertices[i].color = color;
    }
    
    try {
        // 重新上传顶点数据到 GPU
        _sphereVertexBuffer->setBufferAt(*_engine, 0,
            VertexBuffer::BufferDescriptor(_sphereVertices.data(), 
                                         _sphereVertices.size() * sizeof(SphereVertex)));
        
        NSLog(@"✅ 球体顶点颜色更新成功，共更新 %zu 个顶点", _sphereVertices.size());
        return YES;
        
    } catch (const std::exception& e) {
        NSLog(@"❌ 更新顶点颜色时发生异常: %s", e.what());
        return NO;
    } catch (...) {
        NSLog(@"❌ 更新顶点颜色时发生未知异常");
        return NO;
    }
}

// 根据纹理索引设置材质颜色（简化版本，现在主要调用 simulateTextureWithColor）
- (void)setMaterialColorForTextureIndex:(int)index material:(MaterialInstance*)material {
    if (!material) return;
    
    NSLog(@"🔄 使用简化颜色设置方法 (索引: %d)", index);
    
    // 直接调用我们改进的纹理模拟方法
    BOOL success = [self simulateTextureWithColor:material textureIndex:index];
    
    if (!success) {
        NSLog(@"⚠️ 纹理颜色模拟失败，尝试基础颜色设置");
        
        // 降级方案：使用简单的颜色
        static const math::float3 fallbackColors[] = {
            {0.8f, 0.2f, 0.2f}, // 红色
            {0.2f, 0.8f, 0.2f}, // 绿色
            {0.2f, 0.2f, 0.8f}, // 蓝色
            {0.8f, 0.8f, 0.2f}, // 黄色
        };
        
        int colorCount = sizeof(fallbackColors) / sizeof(fallbackColors[0]);
        int colorIndex = index % colorCount;
        math::float3 fallbackColor = fallbackColors[colorIndex];
        
        const Material* mat = material->getMaterial();
        
        if (mat->hasParameter("baseColor")) {
            try {
                material->setParameter("baseColor", RgbType::sRGB, fallbackColor);
                NSLog(@"✅ 设置降级颜色 baseColor: (%.2f, %.2f, %.2f)", 
                      fallbackColor.r, fallbackColor.g, fallbackColor.b);
            } catch (...) {
                NSLog(@"❌ 降级颜色设置也失败了");
            }
        }
    }
}

// ==========================================
// MARK: - 🌐 内置几何体生成
// ==========================================

- (BOOL)loadDefaultSphere {
    if (!_engine) {
        return NO;
    }
    
    // 移除现有模型
    [self removeCurrentModel];
    
    // 球体参数
    const int segments = 32; // 经度分段数
    const int rings = 16;    // 纬度分段数
    const float radius = 1.5f; // 优化球体大小：配合相机距离8.0，占视野约37%
    
    // 计算顶点和索引数量
    const int vertexCount = (rings + 1) * (segments + 1);
    const int indexCount = rings * segments * 6;
    
    // 保存球体参数到类成员变量
    _sphereVertexCount = vertexCount;
    _sphereVertices.resize(vertexCount);
    
    std::vector<uint16_t> indices(indexCount);
    
    // 生成球体顶点
    int vertexIndex = 0;
    // 默认颜色：蓝色
    math::float4 defaultColor = {0.3f, 0.5f, 0.9f, 1.0f}; // 蓝色 RGB(77, 128, 230)
    
    for (int ring = 0; ring <= rings; ring++) {
        float phi = M_PI * ring / rings; // 纬度角
        float y = radius * cos(phi);
        float ringRadius = radius * sin(phi);
        
        for (int segment = 0; segment <= segments; segment++) {
            float theta = 2.0f * M_PI * segment / segments; // 经度角
            float x = ringRadius * cos(theta);
            float z = ringRadius * sin(theta);
            
            // 位置
            _sphereVertices[vertexIndex].position = {x, y, z};
            
            // 简化的切线空间（对于球体，我们可以使用简单的四元数）
            // 这里使用一个简化的切线四元数，让 Filament 处理法向量
            _sphereVertices[vertexIndex].tangents = {0.0f, 0.0f, 0.0f, 1.0f};
            
            // UV 坐标
            float u = (float)segment / segments;
            float v = (float)ring / rings;
            _sphereVertices[vertexIndex].uv = {u, v};
            
            // 顶点颜色 - 默认为白色，稍后可以通过更新缓冲区来改变
            _sphereVertices[vertexIndex].color = defaultColor;
            
            vertexIndex++;
        }
    }
    
    // 生成球体索引
    int indexIndex = 0;
    for (int ring = 0; ring < rings; ring++) {
        for (int segment = 0; segment < segments; segment++) {
            int current = ring * (segments + 1) + segment;
            int next = current + segments + 1;
            
            // 第一个三角形（逆时针）
            indices[indexIndex++] = current;
            indices[indexIndex++] = current + 1;
            indices[indexIndex++] = next;
            
            // 第二个三角形（逆时针）
            indices[indexIndex++] = current + 1;
            indices[indexIndex++] = next + 1;
            indices[indexIndex++] = next;
        }
    }
    
    // 创建顶点缓冲区 - 添加颜色属性
    _sphereVertexBuffer = VertexBuffer::Builder()
        .vertexCount(vertexCount)
        .bufferCount(1)
        .attribute(VertexAttribute::POSITION, 0, VertexBuffer::AttributeType::FLOAT3, 0, sizeof(SphereVertex))
        .attribute(VertexAttribute::TANGENTS, 0, VertexBuffer::AttributeType::FLOAT4, sizeof(math::float3), sizeof(SphereVertex))
        .attribute(VertexAttribute::UV0, 0, VertexBuffer::AttributeType::FLOAT2, sizeof(math::float3) + sizeof(math::float4), sizeof(SphereVertex))
        .attribute(VertexAttribute::COLOR, 0, VertexBuffer::AttributeType::FLOAT4, sizeof(math::float3) + sizeof(math::float4) + sizeof(math::float2), sizeof(SphereVertex))
        .build(*_engine);
    
    // 创建索引缓冲区
    _sphereIndexBuffer = IndexBuffer::Builder()
        .indexCount(indexCount)
        .bufferType(IndexBuffer::IndexType::USHORT)
        .build(*_engine);
    
    // 上传顶点数据
    _sphereVertexBuffer->setBufferAt(*_engine, 0,
        VertexBuffer::BufferDescriptor(_sphereVertices.data(), _sphereVertices.size() * sizeof(SphereVertex)));
    
    // 上传索引数据
    _sphereIndexBuffer->setBuffer(*_engine,
        IndexBuffer::BufferDescriptor(indices.data(), indices.size() * sizeof(uint16_t)));
    
    // 创建一个简单的有色材质来替代默认材质
    if (!_sphereMaterial) {
        _sphereMaterial = [self createSimpleMaterial];
    }
    NSLog(@"球体几何体创建完成: %d 顶点, %d 索引", vertexCount, indexCount);
    NSLog(@"使用自定义简单材质创建球体");
    
    // 创建实体和可渲染组件
    _modelEntity = _engine->getEntityManager().create();
    
    // 构建可渲染组件
    
    RenderableManager::Builder(1)
        .boundingBox({{-radius, -radius, -radius}, {radius, radius, radius}})
        //.material(0, _sphereMaterial)
        .material(0, _currentMaterial)
        .geometry(0, RenderableManager::PrimitiveType::TRIANGLES, _sphereVertexBuffer, _sphereIndexBuffer)
        .culling(false) // 禁用背面剔除，确保内部也可见
        .receiveShadows(true) // 暂时禁用阴影以简化调试
        .castShadows(false)
        .build(*_engine, _modelEntity);
    
    // 添加到场景
    _scene->addEntity(_modelEntity);
    
    NSLog(@"球体实体已添加到场景，实体ID: %d", _modelEntity.getId());
    NSLog(@"场景中实体数量: %zu", _scene->getEntityCount());
    
    return YES;
}

// ==========================================
// MARK: - 🖼️ 纹理管理系统
// ==========================================

- (BOOL)loadTextureFromUIImage:(UIImage *)image {
    if (!_engine || !image || !image.CGImage) {
        return NO;
    }
    
    CGImageRef cgImage = image.CGImage;
    size_t width = CGImageGetWidth(cgImage);
    size_t height = CGImageGetHeight(cgImage);
    
    NSLog(@"加载纹理：%zux%zu", width, height);
    
    // Filament 推荐的纹理格式和大小
    size_t bytesPerPixel = 4; // RGBA
    size_t bytesPerRow = width * bytesPerPixel;
    size_t totalBytes = height * bytesPerRow;
    
    // 创建像素数据缓冲
    std::vector<uint8_t> pixelData(totalBytes);
    
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    CGContextRef context = CGBitmapContextCreate(
        pixelData.data(),
        width, height,
        8, bytesPerRow,
        colorSpace,
        kCGImageAlphaPremultipliedLast | kCGBitmapByteOrder32Big
    );
    
    if (!context) {
        CGColorSpaceRelease(colorSpace);
        return NO;
    }
    
    CGContextDrawImage(context, CGRectMake(0, 0, width, height), cgImage);
    CGContextRelease(context);
    CGColorSpaceRelease(colorSpace);
    NSLog(@"  texture count: %zu", _engine->getTextureCount());
    // 创建 Filament 纹理
    Texture* texture = Texture::Builder()
        .width((uint32_t)width)
        .height((uint32_t)height)
        .levels(1)
        .format(Texture::InternalFormat::RGBA8)
        .sampler(Texture::Sampler::SAMPLER_2D)
        .build(*_engine);
    NSLog(@"  texture count: %zu", _engine->getTextureCount());
    if (!texture) {
        return NO;
    }
    
    // 上传纹理数据
    backend::PixelBufferDescriptor buffer(
        pixelData.data(),
        totalBytes,
        backend::PixelDataFormat::RGBA,
        backend::PixelDataType::UBYTE
    );
    
    texture->setImage(*_engine, 0, std::move(buffer));
    
    // 添加到纹理列表
    _textures.push_back(texture);
    
    return YES;
}

- (void)clearTextures {
    if (_engine) {
        for (Texture* texture : _textures) {
            _engine->destroy(texture);
        }
    }
    _textures.clear();
    _activeTextureIndex = 0;
}

- (BOOL)selectTextureAtIndex:(NSInteger)index {
    if (index < 0 || index >= (NSInteger)_textures.size()) {
        return NO;
    }
    
    _activeTextureIndex = (int)index;
    [self updateMaterialTexture];
    return YES;
}

- (NSInteger)getTextureCount {
    return (NSInteger)_textures.size();
}

- (void)updateMaterialTexture {
    if (!_currentAsset) {
        return;
    }
    size_t entityCount = _currentAsset->getEntityCount();
    auto entities = _currentAsset->getEntities();
    if (_textures.empty() ||
        _activeTextureIndex >= (int)_textures.size() ||
        //_modelEntity.isNull() ||
        entityCount == 0 ||
        !_engine) {
        NSLog(@"⚠️ 无法更新材质纹理：纹理列表为空(%zu)，索引(%d)，或实体无效(%d)",
              _textures.size(), _activeTextureIndex, _modelEntity.getId());
        return;
    }
    
    // 获取当前材质实例
    MaterialInstance* currentMaterial = nullptr;
    if (_sphereMaterial) {
        currentMaterial = _sphereMaterial;
        NSLog(@"使用球体材质");
    } else if (_currentMaterial) {
        currentMaterial = _currentMaterial;
        NSLog(@"使用当前材质");
    }
    
    if (!currentMaterial) {
        NSLog(@"❌ 没有可用的材质实例");
        return;
    }
    
    Texture* activeTexture = _textures[_activeTextureIndex];
    if (!activeTexture) {
        NSLog(@"❌ 活动纹理为空，索引: %d", _activeTextureIndex);
        return;
    }
    
    // 获取材质信息
    const Material* material = currentMaterial->getMaterial();
    NSLog(@"🎨 开始更新材质纹理");
    NSLog(@"   材质名称: %s", material->getName());
    NSLog(@"   纹理索引: %d/%zu", _activeTextureIndex, _textures.size());
    
    // 方案1：尝试直接应用纹理
    NSLog(@"  texture count: %zu", _engine->getTextureCount());
    BOOL textureApplied = [self tryApplyTextureToMaterial:currentMaterial texture:activeTexture];
    //currentMaterial->setParameter("baseColor", RgbType::LINEAR, math::float3{1.0f, 0.9f, 0.8f});
    //currentMaterial->setParameter("metallic", 1.0f);
    //currentMaterial->setParameter("roughness", 0.4f);
    NSLog(@"  texture count: %zu", _engine->getTextureCount());
    if (textureApplied) {
        NSLog(@"✅ 成功应用纹理到材质");
        
        // 纹理应用成功后，确保其他参数也合适
        if (material->hasParameter("baseColorFactor")) {
            try {
                currentMaterial->setParameter("baseColorFactor", 1.0f);
                NSLog(@"设置 baseColorFactor = 1.0");
            } catch (...) {}
        }
        
    } else {
        // 方案2：使用颜色变化表示不同纹理
        NSLog(@"⚠️ 材质不支持纹理参数，使用颜色变化表示纹理加载");
        [self setMaterialColorForTextureIndex:_activeTextureIndex material:currentMaterial];
    }
    
    NSLog(@"🎨 材质纹理更新完成，索引: %d", _activeTextureIndex);
    
    // 强制引擎更新渲染状态
    // 注意：Filament 通常会自动处理这些更新，但我们可以显式通知
    
    if (_scene) {
//        if (!_modelEntity.isNull()) {
//            // 重新添加实体到场景以触发更新（如果需要）
//            _scene->remove(_modelEntity);
//            _scene->addEntity(_modelEntity);
//        }
        if (entityCount > 0) {
            _scene->removeEntities(entities, entityCount);
            _scene->addEntities(entities, entityCount);
        }
        NSLog(@"场景中实体状态正常");
    }
}

// ==========================================
// MARK: - 🎭 纹理变换和动画
// ==========================================

- (void)moveTextureWithDeltaX:(float)deltaX deltaY:(float)deltaY {
    _textureOffset.x += deltaX;
    _textureOffset.y += deltaY;
    [self updateMaterialTexture];
}

- (void)resetTexturePosition {
    _textureOffset = {0.0f, 0.0f};
    [self updateMaterialTexture];
}

// MARK: - 动画控制

- (void)startTextureAnimationWithSpeed:(float)speed {
    [self stopTextureAnimation];
    
    _animationTimer = [NSTimer scheduledTimerWithTimeInterval:1.0/60.0
                                                      repeats:YES
                                                        block:^(NSTimer * _Nonnull timer) {
        [self moveTextureWithDeltaX:0.0f deltaY:0.1];
    }];
}

- (void)stopTextureAnimation {
    if (_animationTimer) {
        [_animationTimer invalidate];
        _animationTimer = nil;
    }
}

@end
