import UIKit
import MetalKit
import PhotosUI
import UniformTypeIdentifiers

class MainViewController: UIViewController {
    
    // MARK: - UI 组件
    private var metalView: MTKView!
    private var controlPanel: UIView!
    private var textureCollectionView: UICollectionView!
    private var loadImageButton: UIButton!
    private var loadModelButton: UIButton!
    private var animateButton: UIButton!
    private var resetButton: UIButton!
    private var sphereButton: UIButton!
    
    // MARK: - 渲染器
    private var filamentRenderer: FilamentRenderer!
    
    // MARK: - 数据
    private var loadedImages: [UIImage] = []
    private var selectedTextureIndex: Int = -1
    private var isAnimating = false
    
    // MARK: - 视图生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupRenderer()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        updateRendererSize()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        
        // 自动加载默认球体以便用户立即看到效果
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            self?.loadDefaultSphere()
        }
    }
    
    private func loadDefaultSphere() {
        let printCallback = { success in
            if success {
                print("默认球体已自动加载")
            } else {
                print("默认球体自动加载失败")
            }
        }
//        filamentRenderer.loadEmbededGlbModel(name: "sphere", completion: printCallback)
        //filamentRenderer.loadEmbededGlbModel(name: "cube", completion: printCallback)
        filamentRenderer.loadDefaultSphere(completion: printCallback)
    }
    
    // MARK: - UI 设置
    private func setupUI() {
        view.backgroundColor = .systemBackground
        title = "3D 纹理渲染器"
        
        setupMetalView()
        setupControlPanel()
        setupButtons()
        setupTextureCollectionView()
        setupConstraints()
    }
    
    private func setupMetalView() {
        metalView = MTKView()
        metalView.translatesAutoresizingMaskIntoConstraints = false
        metalView.device = MTLCreateSystemDefaultDevice()
        metalView.clearColor = MTLClearColor(red: 0.2, green: 0.2, blue: 0.3, alpha: 1.0)
        metalView.delegate = self
        view.addSubview(metalView)
        
        // 添加手势识别
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePanGesture(_:)))
        metalView.addGestureRecognizer(panGesture)
    }
    
    private func setupControlPanel() {
        controlPanel = UIView()
        controlPanel.translatesAutoresizingMaskIntoConstraints = false
        controlPanel.backgroundColor = .systemBackground
        controlPanel.layer.borderColor = UIColor.systemGray4.cgColor
        controlPanel.layer.borderWidth = 1
        controlPanel.layer.cornerRadius = 8
        view.addSubview(controlPanel)
    }
    
    private func setupButtons() {
        // 加载图片按钮
        loadImageButton = UIButton(type: .system)
        loadImageButton.translatesAutoresizingMaskIntoConstraints = false
        loadImageButton.setTitle("加载图片", for: .normal)
        loadImageButton.backgroundColor = .systemBlue
        loadImageButton.setTitleColor(.white, for: .normal)
        loadImageButton.layer.cornerRadius = 8
        loadImageButton.addTarget(self, action: #selector(loadImageTapped), for: .touchUpInside)
        controlPanel.addSubview(loadImageButton)
        
        // 加载模型按钮
        loadModelButton = UIButton(type: .system)
        loadModelButton.translatesAutoresizingMaskIntoConstraints = false
        loadModelButton.setTitle("加载模型", for: .normal)
        loadModelButton.backgroundColor = .systemGreen
        loadModelButton.setTitleColor(.white, for: .normal)
        loadModelButton.layer.cornerRadius = 8
        loadModelButton.addTarget(self, action: #selector(loadModelTapped), for: .touchUpInside)
        controlPanel.addSubview(loadModelButton)
        
        // 动画按钮
        animateButton = UIButton(type: .system)
        animateButton.translatesAutoresizingMaskIntoConstraints = false
        animateButton.setTitle("开始动画", for: .normal)
        animateButton.backgroundColor = .systemOrange
        animateButton.setTitleColor(.white, for: .normal)
        animateButton.layer.cornerRadius = 8
        animateButton.addTarget(self, action: #selector(animateTapped), for: .touchUpInside)
        controlPanel.addSubview(animateButton)
        
        // 重置按钮
        resetButton = UIButton(type: .system)
        resetButton.translatesAutoresizingMaskIntoConstraints = false
        resetButton.setTitle("重置位置", for: .normal)
        resetButton.backgroundColor = .systemRed
        resetButton.setTitleColor(.white, for: .normal)
        resetButton.layer.cornerRadius = 8
        resetButton.addTarget(self, action: #selector(resetTapped), for: .touchUpInside)
        controlPanel.addSubview(resetButton)
        
        // 球体按钮
        sphereButton = UIButton(type: .system)
        sphereButton.translatesAutoresizingMaskIntoConstraints = false
        sphereButton.setTitle("加载球体", for: .normal)
        sphereButton.backgroundColor = .systemPurple
        sphereButton.setTitleColor(.white, for: .normal)
        sphereButton.layer.cornerRadius = 8
        sphereButton.addTarget(self, action: #selector(sphereTapped), for: .touchUpInside)
        controlPanel.addSubview(sphereButton)
    }
    
    private func setupTextureCollectionView() {
        let layout = UICollectionViewFlowLayout()
        layout.itemSize = CGSize(width: 60, height: 60)
        layout.minimumInteritemSpacing = 8
        layout.scrollDirection = .horizontal
        
        textureCollectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        textureCollectionView.translatesAutoresizingMaskIntoConstraints = false
        textureCollectionView.backgroundColor = .clear
        textureCollectionView.delegate = self
        textureCollectionView.dataSource = self
        textureCollectionView.register(TextureCell.self, forCellWithReuseIdentifier: "TextureCell")
        controlPanel.addSubview(textureCollectionView)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // Metal 视图约束
            metalView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            metalView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            metalView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            metalView.bottomAnchor.constraint(equalTo: controlPanel.topAnchor, constant: -8),
            
            // 控制面板约束
            controlPanel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 16),
            controlPanel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -16),
            controlPanel.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -8),
            controlPanel.heightAnchor.constraint(equalToConstant: 140),
            
            // 第一行按钮约束
            loadImageButton.topAnchor.constraint(equalTo: controlPanel.topAnchor, constant: 8),
            loadImageButton.leadingAnchor.constraint(equalTo: controlPanel.leadingAnchor, constant: 8),
            loadImageButton.widthAnchor.constraint(equalToConstant: 80),
            loadImageButton.heightAnchor.constraint(equalToConstant: 36),
            
            loadModelButton.topAnchor.constraint(equalTo: controlPanel.topAnchor, constant: 8),
            loadModelButton.leadingAnchor.constraint(equalTo: loadImageButton.trailingAnchor, constant: 8),
            loadModelButton.widthAnchor.constraint(equalToConstant: 80),
            loadModelButton.heightAnchor.constraint(equalToConstant: 36),
            
            sphereButton.topAnchor.constraint(equalTo: controlPanel.topAnchor, constant: 8),
            sphereButton.leadingAnchor.constraint(equalTo: loadModelButton.trailingAnchor, constant: 8),
            sphereButton.widthAnchor.constraint(equalToConstant: 80),
            sphereButton.heightAnchor.constraint(equalToConstant: 36),
            
            // 第二行按钮约束
            animateButton.topAnchor.constraint(equalTo: loadImageButton.bottomAnchor, constant: 8),
            animateButton.leadingAnchor.constraint(equalTo: controlPanel.leadingAnchor, constant: 8),
            animateButton.widthAnchor.constraint(equalToConstant: 80),
            animateButton.heightAnchor.constraint(equalToConstant: 36),
            
            resetButton.topAnchor.constraint(equalTo: loadImageButton.bottomAnchor, constant: 8),
            resetButton.leadingAnchor.constraint(equalTo: animateButton.trailingAnchor, constant: 8),
            resetButton.widthAnchor.constraint(equalToConstant: 80),
            resetButton.heightAnchor.constraint(equalToConstant: 36),
            
            // 纹理集合视图约束
            textureCollectionView.topAnchor.constraint(equalTo: animateButton.bottomAnchor, constant: 8),
            textureCollectionView.leadingAnchor.constraint(equalTo: controlPanel.leadingAnchor, constant: 8),
            textureCollectionView.trailingAnchor.constraint(equalTo: controlPanel.trailingAnchor, constant: -8),
            textureCollectionView.bottomAnchor.constraint(equalTo: controlPanel.bottomAnchor, constant: -8)
        ])
    }
    
    // MARK: - 渲染器设置
    private func setupRenderer() {
        filamentRenderer = FilamentRenderer()
        if let metalLayer = metalView.layer as? CAMetalLayer {
            filamentRenderer.setupSurface(metalLayer)
        }
    }
    
    private func updateRendererSize() {
        let scale = UIScreen.main.scale
        let width = UInt32(metalView.bounds.width * scale)
        let height = UInt32(metalView.bounds.height * scale)
        filamentRenderer.resize(width: width, height: height)
    }
    
    // MARK: - 按钮事件处理
    @objc private func loadImageTapped() {
        var configuration = PHPickerConfiguration()
        configuration.filter = .images
        configuration.selectionLimit = 0 // 允许多选
        
        let picker = PHPickerViewController(configuration: configuration)
        picker.delegate = self
        present(picker, animated: true)
    }
    
    @objc private func loadModelTapped() {
        // 定义支持的 3D 模型文件类型
        let supportedTypes = [
            UTType(filenameExtension: "gltf")!,
            UTType(filenameExtension: "glb")!,
            UTType.data // 备用选项
        ]
        
        let documentPicker = UIDocumentPickerViewController(forOpeningContentTypes: supportedTypes)
        documentPicker.delegate = self
        documentPicker.allowsMultipleSelection = false
        documentPicker.shouldShowFileExtensions = true
        present(documentPicker, animated: true)
    }
    
    @objc private func animateTapped() {
        if isAnimating {
            filamentRenderer.stopTextureAnimation()
            animateButton.setTitle("开始动画", for: .normal)
        } else {
            filamentRenderer.startTextureAnimation()
            animateButton.setTitle("停止动画", for: .normal)
        }
        isAnimating.toggle()
    }
    
    @objc private func resetTapped() {
        filamentRenderer.resetTexturePosition()
        filamentRenderer.removeModel()
    }
    
    @objc private func sphereTapped() {
        filamentRenderer.loadDefaultSphere { [weak self] success in
            if success {
                self?.showAlert(title: "成功", message: "内置球体模型加载成功")
            } else {
                self?.showAlert(title: "错误", message: "球体模型创建失败")
            }
        }
    }
    
    // MARK: - 手势处理
    @objc private func handlePanGesture(_ gesture: UIPanGestureRecognizer) {
        let translation = gesture.translation(in: metalView)
        let velocity = gesture.velocity(in: metalView)
        
        switch gesture.state {
        case .changed:
            let deltaX = Float(translation.x) * 0.001
            let deltaY = Float(-translation.y) * 0.001 // Y 轴翻转
            filamentRenderer.moveTexture(deltaX: deltaX, deltaY: deltaY)
            gesture.setTranslation(.zero, in: metalView)
            
        default:
            break
        }
    }
    
    // MARK: - 辅助方法
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - MTKViewDelegate
extension MainViewController: MTKViewDelegate {
    func mtkView(_ view: MTKView, drawableSizeWillChange size: CGSize) {
        updateRendererSize()
    }
    
    func draw(in view: MTKView) {
        filamentRenderer.render()
    }
}

// MARK: - PHPickerViewControllerDelegate
extension MainViewController: PHPickerViewControllerDelegate {
    func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
        picker.dismiss(animated: true)
        
        for result in results {
            result.itemProvider.loadObject(ofClass: UIImage.self) { [weak self] object, error in
                if let image = object as? UIImage {
                    DispatchQueue.main.async {
                        self?.loadedImages.append(image)
                        self?.textureCollectionView.reloadData()
                        
                        // 加载到渲染器
                        self?.filamentRenderer.loadTexture(from: image) { success in
                            if success {
                                print("纹理加载成功")
                            } else {
                                self?.showAlert(title: "错误", message: "纹理加载失败")
                            }
                        }
                    }
                }
            }
        }
    }
}

// MARK: - UIDocumentPickerDelegate
extension MainViewController: UIDocumentPickerDelegate {
    func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
        guard let url = urls.first else { return }
        
        // 开始访问沙盒保护的资源
        guard url.startAccessingSecurityScopedResource() else {
            showAlert(title: "权限错误", message: "无法访问选择的文件")
            return
        }
        
        // 确保在完成后停止访问
        defer {
            url.stopAccessingSecurityScopedResource()
        }
        
        do {
            // 检查文件是否存在
            guard (try? url.checkResourceIsReachable()) == true else {
                showAlert(title: "文件错误", message: "选择的文件不可访问")
                return
            }
            
            // 读取文件数据
            let data = try Data(contentsOf: url)
            
            // 检查文件大小（避免加载过大文件）
            let maxSize = 50 * 1024 * 1024 // 50MB 限制
            guard data.count <= maxSize else {
                showAlert(title: "文件过大", message: "模型文件大小超过 50MB 限制")
                return
            }
            
            // 加载模型
            DispatchQueue.main.async {
                self.filamentRenderer.loadModel(from: data) { [weak self] success in
                    if success {
                        self?.showAlert(title: "成功", message: "模型加载成功\n文件大小: \(self?.formatFileSize(data.count) ?? "未知")")
                    } else {
                        self?.showAlert(title: "错误", message: "模型加载失败\n请确保文件是有效的 glTF/GLB 格式")
                    }
                }
            }
        } catch {
            showAlert(title: "读取错误", message: "无法读取文件: \(error.localizedDescription)")
        }
    }
    
    func documentPickerWasCancelled(_ controller: UIDocumentPickerViewController) {
        // 用户取消选择
        print("用户取消了文件选择")
    }
    
    // 格式化文件大小显示
    private func formatFileSize(_ bytes: Int) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB, .useKB, .useBytes]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: Int64(bytes))
    }
}

// MARK: - UICollectionViewDataSource & UICollectionViewDelegate
extension MainViewController: UICollectionViewDataSource, UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return loadedImages.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "TextureCell", for: indexPath) as! TextureCell
        cell.configure(with: loadedImages[indexPath.item], isSelected: indexPath.item == selectedTextureIndex)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        selectedTextureIndex = indexPath.item
        textureCollectionView.reloadData()
        
        if filamentRenderer.selectTexture(at: indexPath.item) {
            print("已选择纹理 \(indexPath.item)")
        }
    }
}

// MARK: - 纹理单元格
class TextureCell: UICollectionViewCell {
    private let imageView = UIImageView()
    private let selectionView = UIView()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupCell()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupCell() {
        imageView.translatesAutoresizingMaskIntoConstraints = false
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 8
        contentView.addSubview(imageView)
        
        selectionView.translatesAutoresizingMaskIntoConstraints = false
        selectionView.backgroundColor = UIColor.systemBlue.withAlphaComponent(0.3)
        selectionView.layer.cornerRadius = 8
        selectionView.isHidden = true
        contentView.addSubview(selectionView)
        
        NSLayoutConstraint.activate([
            imageView.topAnchor.constraint(equalTo: contentView.topAnchor),
            imageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            imageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            imageView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            
            selectionView.topAnchor.constraint(equalTo: contentView.topAnchor),
            selectionView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            selectionView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            selectionView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor)
        ])
    }
    
    func configure(with image: UIImage, isSelected: Bool) {
        imageView.image = image
        selectionView.isHidden = !isSelected
    }
}
