import Foundation
import MetalKit

class FilamentRenderer: NSObject {
    
    // MARK: - Filament 包装器
    private var filamentWrapper: FilamentWrapper
    
    // MARK: - 初始化
    override init() {
        filamentWrapper = FilamentWrapper()
        super.init()
    }
    
    deinit {
        // FilamentWrapper 会自动清理
    }
    
    // MARK: - 表面管理
    func setupSurface(_ metalLayer: CAMetalLayer) {
        _ = filamentWrapper.setup(with: metalLayer)
    }
    
    func resize(width: UInt32, height: UInt32) {
        filamentWrapper.resize(withWidth: UInt(width), height: UInt(height))
    }
    
    // MARK: - 渲染
    func render() {
        filamentWrapper.render()
    }
    
    // MARK: - 模型卸载
    
    func removeModel() {
        self.filamentWrapper.removeCurrentModel()
    }
    
    // MARK: - 模型加载
    func loadModel(from data: Data, completion: @escaping (Bool) -> Void) {
//        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
//            guard let self = self else {
//                DispatchQueue.main.async {
//                    completion(false)
//                }
//                return
//            }
            
            let success = self.filamentWrapper.loadModel(from: data)
            
//            DispatchQueue.main.async {
                completion(success)
//            }
//        }
    }
    
    func loadDefaultSphere(completion: @escaping (Bool) -> Void) {
//        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
//            guard let self = self else {
//                DispatchQueue.main.async {
//                    completion(false)
//                }
//                return
//            }
//            
//            let success = self.filamentWrapper.loadDefaultSphere()
//            DispatchQueue.main.async {
//                completion(success)
//            }
//        }
        
        loadEmbededGlbModel(name: "sphere", completion: completion)
        //loadEmbededGlbModel(name: "McLaren", completion: completion)
    }
    
    func loadEmbededGlbModel(name :String, completion: @escaping (Bool) -> Void) {
        var result = false
        
        if name.lengthOfBytes(using: .utf8) > 0 {
            if let glbPath = Bundle.main.path(forResource: name, ofType: "glb", inDirectory: "models") {
                if let data = NSData.init(contentsOfFile: glbPath) {
                    result = self.filamentWrapper.loadModel(from: data as Data)
                }
            }
        }
        
        DispatchQueue.main.async {
            completion(result)
        }

    }
    
    
    // MARK: - 纹理管理
    func loadTexture(from image: UIImage, completion: @escaping (Bool) -> Void) {
//        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
//            guard let self = self else {
//                DispatchQueue.main.async {
//                    completion(false)
//                }
//                return
//            }
            
            let success = self.filamentWrapper.loadTexture(from: image)
            
//            DispatchQueue.main.async {
                completion(success)
//            }
//        }
    }
    
    func clearTextures() {
        filamentWrapper.clearTextures()
    }
    
    func selectTexture(at index: Int) -> Bool {
        return filamentWrapper.selectTexture(at: index)
    }
    
    func getTextureCount() -> Int {
        return Int(filamentWrapper.getTextureCount())
    }
    
    // MARK: - 纹理变换
    func moveTexture(deltaX: Float, deltaY: Float) {
        filamentWrapper.moveTexture(withDeltaX: deltaX, deltaY: deltaY)
    }
    
    func resetTexturePosition() {
        filamentWrapper.resetTexturePosition()
    }
    
    // MARK: - 动画控制
    func startTextureAnimation(speed: Float = 0.01) {
        filamentWrapper.startTextureAnimation(withSpeed: speed)
    }
    
    func stopTextureAnimation() {
        filamentWrapper.stopTextureAnimation()
    }
}

// MARK: - 数学工具函数（移除了，因为现在在 C++ 层处理）
