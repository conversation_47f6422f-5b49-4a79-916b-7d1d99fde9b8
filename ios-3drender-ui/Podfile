platform :ios, '13.0'

target 'iOS3DRenderUI' do
  use_frameworks!

  # Filament 3D 渲染引擎
  # pod 'Filament', '1.56.6'
   pod 'Filament', '1.62.2'

#  pod 'Filament', :podspec => 'https://raw.githubusercontent.com/google/filament/refs/tags/v1.62.2/ios/CocoaPods/Filament.podspec', :subspec => [
#    "filament", "filamat", "gltfio_core", "camutils", "filameshio", "image", "utils"
#  ]
  
  target 'iOS3DRenderUITests' do
    inherit! :search_paths
  end
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
      config.build_settings['ENABLE_BITCODE'] = 'NO'
      # 确保所有目标使用正确的 C++ 标准
      config.build_settings['CLANG_CXX_LANGUAGE_STANDARD'] = 'c++17'
      config.build_settings['CLANG_CXX_LIBRARY'] = 'libc++'
      config.build_settings['CLANG_ENABLE_MODULES'] = 'YES'
      config.build_settings['GCC_C_LANGUAGE_STANDARD'] = 'c11'
    end
  end
end
