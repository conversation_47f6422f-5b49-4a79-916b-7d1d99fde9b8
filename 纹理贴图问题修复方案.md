# 纹理贴图问题修复方案

## 🚨 问题诊断结果

### 发现的关键问题：
1. **材质管理逻辑混乱** - 先设置纹理再替换材质实例，导致设置丢失
2. **默认材质不支持纹理** - Filament默认材质通常不支持baseColorMap参数  
3. **光照设置可能导致全黑** - lit材质配合不足的光照会显示为黑色
4. **纹理应用逻辑重复** - 同时操作多个材质实例造成冲突

## 🔧 修复方案

### 方案1：修复 tryApplyTextureToMaterial 方法

```objc
// 修复后的纹理应用方法
- (BOOL)tryApplyTextureToMaterial:(MaterialInstance*)material texture:(Texture*)texture {
    if (!material || !texture) return NO;
    
    const Material* mat = material->getMaterial();
    NSLog(@"🎨 应用纹理到材质: %s", mat->getName());
    
    // 创建纹理采样器
    TextureSampler sampler = TextureSampler(
        TextureSampler::MinFilter::LINEAR,
        TextureSampler::MagFilter::LINEAR,
        TextureSampler::WrapMode::REPEAT
    );
    
    // 检查材质支持的纹理参数
    NSArray* textureParams = @[@"baseColorMap", @"diffuseMap", @"albedoMap", @"colorMap"];
    
    for (NSString* paramName in textureParams) {
        const char* cParamName = [paramName UTF8String];
        if (mat->hasParameter(cParamName)) {
            try {
                material->setParameter(cParamName, texture, sampler);
                NSLog(@"✅ 成功设置纹理参数: %@", paramName);
                
                // 确保材质其他参数适合纹理显示
                if (mat->hasParameter("baseColorFactor")) {
                    material->setParameter("baseColorFactor", 1.0f);
                }
                
                return YES;
            } catch (const std::exception& e) {
                NSLog(@"❌ 设置纹理参数 %@ 失败: %s", paramName, e.what());
            }
        }
    }
    
    NSLog(@"⚠️ 材质不支持纹理参数，需要使用支持纹理的材质");
    return NO;
}
```

### 方案2：修复材质创建和应用逻辑

```objc
// 修复后的材质更新方法
- (void)updateMaterialTexture {
    if (_textures.empty() || _activeTextureIndex >= (int)_textures.size() || !_engine) {
        NSLog(@"⚠️ 无法更新纹理：纹理列表为空或索引无效");
        return;
    }
    
    Texture* activeTexture = _textures[_activeTextureIndex];
    if (!activeTexture) {
        NSLog(@"❌ 活动纹理为空");
        return;
    }
    
    // 确保使用支持纹理的材质
    if (!_currentMaterial) {
        _currentMaterial = [self createTextureEnabledMaterial];
        if (!_currentMaterial) {
            NSLog(@"❌ 无法创建支持纹理的材质");
            return;
        }
    }
    
    // 为当前材质设置纹理
    BOOL success = [self tryApplyTextureToMaterial:_currentMaterial texture:activeTexture];
    if (!success) {
        NSLog(@"⚠️ 纹理应用失败，可能需要重新创建材质");
        return;
    }
    
    // 将材质应用到所有渲染对象
    [self applyMaterialToRenderables:_currentMaterial];
    
    NSLog(@"✅ 纹理更新完成，索引: %d", _activeTextureIndex);
}

// 新增：应用材质到渲染对象
- (void)applyMaterialToRenderables:(MaterialInstance*)material {
    if (!material) return;
    
    auto& rcm = _engine->getRenderableManager();
    
    // 如果是内置球体
    if (!_modelEntity.isNull() && rcm.hasComponent(_modelEntity)) {
        auto instance = rcm.getInstance(_modelEntity);
        size_t primCount = rcm.getPrimitiveCount(instance);
        for (size_t i = 0; i < primCount; ++i) {
            rcm.setMaterialInstanceAt(instance, i, material);
        }
        NSLog(@"✅ 材质应用到内置球体");
        return;
    }
    
    // 如果是加载的模型
    if (_currentAsset) {
        auto const& entities = _currentAsset->getRenderableEntities();
        auto const& renderableCount = _currentAsset->getRenderableEntityCount();
        
        for (size_t i = 0; i < renderableCount; i++) {
            auto entity = entities[i];
            if (rcm.hasComponent(entity)) {
                auto instance = rcm.getInstance(entity);
                size_t primCount = rcm.getPrimitiveCount(instance);
                for (size_t j = 0; j < primCount; ++j) {
                    rcm.setMaterialInstanceAt(instance, j, material);
                }
            }
        }
        NSLog(@"✅ 材质应用到加载模型的 %zu 个实体", renderableCount);
    }
}
```

### 方案3：创建专门的纹理支持材质

```objc
// 新增：创建支持纹理的材质
- (MaterialInstance*)createTextureEnabledMaterial {
    NSLog(@"🔧 创建支持纹理的材质...");
    
    // 方案1：尝试加载预编译的纹理材质
    MaterialInstance* instance = [self loadPrecompiledTextureMaterial];
    if (instance) {
        NSLog(@"✅ 使用预编译纹理材质");
        return instance;
    }
    
    // 方案2：创建简单的unlit材质（确保支持纹理）
    instance = [self createUnlitTextureMaterial];
    if (instance) {
        NSLog(@"✅ 使用unlit纹理材质");
        return instance;
    }
    
    // 方案3：降级使用默认材质，但添加警告
    NSLog(@"⚠️ 降级使用默认材质，纹理可能不显示");
    return _engine->getDefaultMaterial()->createInstance();
}

// 加载预编译纹理材质
- (MaterialInstance*)loadPrecompiledTextureMaterial {
    NSString *matPath = [NSBundle.mainBundle pathForResource:@"baseColor" ofType:@"filamat" inDirectory:@"material"];
    if (!matPath) {
        NSLog(@"⚠️ 找不到baseColor.filamat文件");
        return nullptr;
    }
    
    NSData *matData = [NSData dataWithContentsOfFile:matPath];
    if (!matData) {
        NSLog(@"⚠️ 无法读取材质文件");
        return nullptr;
    }
    
    try {
        auto* mat = filament::Material::Builder()
            .package(matData.bytes, matData.length)
            .build(*_engine);
        
        if (mat) {
            NSLog(@"✅ 成功加载预编译材质");
            return mat->createInstance();
        }
    } catch (const std::exception& e) {
        NSLog(@"❌ 加载预编译材质失败: %s", e.what());
    }
    
    return nullptr;
}

// 创建unlit纹理材质（作为备用方案）
- (MaterialInstance*)createUnlitTextureMaterial {
    // 这里需要使用Material::Builder创建一个简单的unlit材质
    // 由于需要shader代码，这个方法比较复杂，建议使用预编译材质
    NSLog(@"⚠️ 创建运行时材质需要shader代码，建议使用预编译材质");
    return nullptr;
}
```

### 方案4：改进光照设置

```objc
// 修改setupLighting方法，确保充足光照
- (void)setupLighting {
    // 创建方向光（更适合纹理展示）
    _lightEntity = _engine->getEntityManager().create();
    
    LightManager::Builder(LightManager::Type::DIRECTIONAL)
        .color(Color::cct(6500.0f))
        .intensity(100000.0f) // 增加光照强度
        .direction({-0.3f, -1.0f, -0.8f}) // 来自右上方的光
        .castShadows(false) // 暂时禁用阴影以简化调试
        .build(*_engine, _lightEntity);
    
    _scene->addEntity(_lightEntity);
    
    // 增加环境光强度
    auto ibl = IndirectLight::Builder()
        .intensity(50000.0f) // 增加环境光强度
        .build(*_engine);
    _scene->setIndirectLight(ibl);
    
    NSLog(@"✅ 优化光照设置完成，增强纹理可见性");
}
```

## 🔍 调试建议

### 1. 添加详细的调试日志
```objc
// 在关键位置添加调试信息
NSLog(@"🔍 纹理信息 - 大小: %dx%d, 格式: RGBA8", width, height);
NSLog(@"🔍 材质信息 - 名称: %s, 支持参数: %@", material->getName(), supportedParams);
NSLog(@"🔍 光照信息 - 类型: %d, 强度: %.0f", lightType, intensity);
```

### 2. 验证纹理数据
```objc
// 验证纹理数据是否正确
- (void)validateTextureData:(NSData*)imageData {
    if (imageData.length == 0) {
        NSLog(@"❌ 纹理数据为空");
        return;
    }
    
    // 检查是否为有效图像格式
    UIImage* testImage = [UIImage imageWithData:imageData];
    if (!testImage) {
        NSLog(@"❌ 无法解析图像数据");
        return;
    }
    
    NSLog(@"✅ 纹理验证通过 - 大小: %.0fx%.0f", testImage.size.width, testImage.size.height);
}
```

### 3. 材质参数检查工具
```objc
// 检查材质支持的所有参数
- (void)debugMaterialParameters:(MaterialInstance*)material {
    const Material* mat = material->getMaterial();
    NSLog(@"🔍 材质调试信息:");
    NSLog(@"   名称: %s", mat->getName());
    NSLog(@"   着色模型: %d", mat->getShadingModel());
    NSLog(@"   是否透明: %d", mat->getTransparencyMode());
    
    // 测试常见参数
    NSArray* commonParams = @[
        @"baseColorMap", @"baseColor", @"baseColorFactor",
        @"diffuseMap", @"albedoMap", @"colorMap",
        @"metallicFactor", @"roughnessFactor"
    ];
    
    for (NSString* param in commonParams) {
        BOOL hasParam = mat->hasParameter([param UTF8String]);
        NSLog(@"   %@: %@", param, hasParam ? @"✅" : @"❌");
    }
}
```

## 📋 实施步骤

1. **立即修复** - 修改`tryApplyTextureToMaterial`方法，移除材质替换逻辑
2. **材质文件** - 确保`baseColor.filamat`文件存在且正确
3. **光照调整** - 增加光照强度，确保模型可见
4. **调试验证** - 添加调试日志，验证每个步骤
5. **测试优化** - 逐步测试纹理加载、应用和显示效果

这套方案应该能够解决你遇到的纹理贴图问题。关键是确保使用支持纹理的材质，并且光照设置充足。